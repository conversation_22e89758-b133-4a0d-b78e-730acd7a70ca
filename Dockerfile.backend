# Dockerfile for Backend API
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PORT=8080
ENV ENVIRONMENT=production

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    curl \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r -g 1001 appuser && useradd -r -g appuser -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY backend/requirements.txt .
RUN pip install --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY backend/ .

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/tmp /app/data/scheduler \
    && chown -R appuser:appuser /app \
    && chmod 755 /app /app/logs /app/tmp /app/data /app/data/scheduler

# Make sure credential files have correct permissions
RUN find . -name "*.json" -exec chmod 600 {} \; \
    && chown appuser:appuser *.json || true

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD python -c "\
import sys, requests; \
try: \
    response = requests.get('http://localhost:8080/health', timeout=8); \
    data = response.json(); \
    sys.exit(0 if data.get('status') in ['healthy', 'degraded'] else 1) \
except Exception as e: \
    print(f'Health check failed: {e}'); \
    sys.exit(1)"

# Expose port
EXPOSE 8080

# Start the application
CMD ["uvicorn", "main:app", \
     "--host", "0.0.0.0", \
     "--port", "8080", \
     "--workers", "1", \
     "--access-log", \
     "--log-level", "info"]
