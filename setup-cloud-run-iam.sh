#!/bin/bash

# Cloud Run IAM Setup Script for Email Analyzer System
# This script sets up service accounts and IAM permissions for secure service communication

set -e

# Configuration
PROJECT_ID=${1:-$(gcloud config get-value project)}
REGION=${2:-"us-central1"}

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: PROJECT_ID is required"
    echo "Usage: $0 <PROJECT_ID> [REGION]"
    exit 1
fi

echo "🚀 Setting up Cloud Run IAM for Email Analyzer System"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"

# Enable required APIs
echo "📡 Enabling required APIs..."
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    pubsub.googleapis.com \
    firestore.googleapis.com \
    secretmanager.googleapis.com \
    --project=$PROJECT_ID

# Create service accounts
echo "👤 Creating service accounts..."

# Backend service account
if ! gcloud iam service-accounts describe email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud iam service-accounts create email-analyzer-backend-sa \
        --display-name="Email Analyzer Backend Service Account" \
        --description="Service account for the Email Analyzer backend API" \
        --project=$PROJECT_ID
    echo "✅ Created backend service account"
else
    echo "ℹ️  Backend service account already exists"
fi

# Pub/Sub handler service account
if ! gcloud iam service-accounts describe email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud iam service-accounts create email-analyzer-pubsub-sa \
        --display-name="Email Analyzer Pub/Sub Handler Service Account" \
        --description="Service account for the Email Analyzer Pub/Sub handler" \
        --project=$PROJECT_ID
    echo "✅ Created Pub/Sub service account"
else
    echo "ℹ️  Pub/Sub service account already exists"
fi

# Grant IAM roles to backend service account
echo "🔐 Configuring backend service account permissions..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/monitoring.metricWriter"

# Grant IAM roles to Pub/Sub handler service account
echo "🔐 Configuring Pub/Sub service account permissions..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/monitoring.metricWriter"

# Allow Pub/Sub handler to invoke backend service
echo "🔗 Setting up service-to-service communication..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.invoker"

# Allow Pub/Sub to invoke the Pub/Sub handler
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:service-$<EMAIL>" \
    --role="roles/run.invoker" || echo "⚠️  Note: You may need to manually grant Pub/Sub service account run.invoker role"

# Create Pub/Sub topic if it doesn't exist
echo "📬 Setting up Pub/Sub topic..."
if ! gcloud pubsub topics describe email-notifications --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud pubsub topics create email-notifications --project=$PROJECT_ID
    echo "✅ Created Pub/Sub topic: email-notifications"
else
    echo "ℹ️  Pub/Sub topic already exists"
fi

# Create secrets in Secret Manager
echo "🔒 Setting up Secret Manager secrets..."

# Service token secret
if ! gcloud secrets describe email-analyzer-service-token --project=$PROJECT_ID >/dev/null 2>&1; then
    # Generate a secure service token
    SERVICE_TOKEN=$(openssl rand -base64 32)
    echo -n "$SERVICE_TOKEN" | gcloud secrets create email-analyzer-service-token \
        --data-file=- \
        --project=$PROJECT_ID
    echo "✅ Created service token secret"
else
    echo "ℹ️  Service token secret already exists"
fi

# Firebase credentials secret (placeholder)
if ! gcloud secrets describe firebase-credentials --project=$PROJECT_ID >/dev/null 2>&1; then
    echo '{"type": "service_account", "project_id": "PLACEHOLDER"}' | gcloud secrets create firebase-credentials \
        --data-file=- \
        --project=$PROJECT_ID
    echo "⚠️  Created placeholder Firebase credentials secret - UPDATE WITH REAL CREDENTIALS"
else
    echo "ℹ️  Firebase credentials secret already exists"
fi

# Gmail credentials secret (placeholder)
if ! gcloud secrets describe gmail-credentials --project=$PROJECT_ID >/dev/null 2>&1; then
    echo '{"installed": {"client_id": "PLACEHOLDER"}}' | gcloud secrets create gmail-credentials \
        --data-file=- \
        --project=$PROJECT_ID
    echo "⚠️  Created placeholder Gmail credentials secret - UPDATE WITH REAL CREDENTIALS"
else
    echo "ℹ️  Gmail credentials secret already exists"
fi

# Gemini API key secret (placeholder)
if ! gcloud secrets describe gemini-api-key --project=$PROJECT_ID >/dev/null 2>&1; then
    echo -n "PLACEHOLDER_GEMINI_API_KEY" | gcloud secrets create gemini-api-key \
        --data-file=- \
        --project=$PROJECT_ID
    echo "⚠️  Created placeholder Gemini API key secret - UPDATE WITH REAL API KEY"
else
    echo "ℹ️  Gemini API key secret already exists"
fi

echo ""
echo "🎉 Cloud Run IAM setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update secrets with real credentials:"
echo "   - gcloud secrets versions add firebase-credentials --data-file=path/to/firebase-key.json"
echo "   - gcloud secrets versions add gmail-credentials --data-file=path/to/gmail-credentials.json"
echo "   - gcloud secrets versions add gemini-api-key --data-file=<(echo -n 'YOUR_GEMINI_API_KEY')"
echo ""
echo "2. Deploy the services:"
echo "   - gcloud builds submit --config=cloudbuild.yaml"
echo ""
echo "3. Configure Gmail push notifications to point to your Pub/Sub handler URL"
echo ""
echo "📊 Service accounts created:"
echo "   - email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com"
echo "   - email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com"
echo ""
echo "🔒 Secrets created:"
echo "   - email-analyzer-service-token"
echo "   - firebase-credentials (UPDATE REQUIRED)"
echo "   - gmail-credentials (UPDATE REQUIRED)"
echo "   - gemini-api-key (UPDATE REQUIRED)"
