# Pub/Sub Handler Environment Configuration for Cloud Run
# This file contains the environment variables for the Pub/Sub handler

# Google Cloud Project Configuration
GOOGLE_CLOUD_PROJECT=ai-email-bot-455814
PUBSUB_TOPIC=projects/ai-email-bot-455814/topics/email-notifications

# Main Backend API Configuration (will be updated after backend deployment)
MAIN_FASTAPI_URL=https://emailbot-**********-uc.a.run.app
SERVICE_TOKEN=your-secure-service-token-here

# Performance and Reliability Settings
MAX_RETRIES=3
RETRY_DELAY=1.0
REQUEST_TIMEOUT=30
MAX_EMAILS_PER_NOTIFICATION=10

# Rate Limiting
RATE_LIMIT_WINDOW=60
MAX_REQUESTS_PER_WINDOW=100
MAX_CONCURRENT_PROCESSING=10

# Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
FIRESTORE_HEALTH_CHECK_TIMEOUT=5

# Logging Configuration
LOG_LEVEL=INFO

# Server Configuration
PORT=8080
HOST=0.0.0.0
ENVIRONMENT=production
WORKERS=1
