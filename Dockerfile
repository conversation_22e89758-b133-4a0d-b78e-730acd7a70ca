# Enhanced Multi-stage build for FastAPI Pub/Sub Handler
FROM python:3.11-slim as builder

# Set environment variables for Python
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libc6-dev \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create and set working directory
WORKDIR /app

# Copy requirements and install Python dependencies with optimizations
COPY requirements-pubsub.txt .
RUN pip install --upgrade pip setuptools wheel \
    && pip install --no-cache-dir --user -r requirements-pubsub.txt \
    && find /root/.local -name "*.pyc" -delete \
    && find /root/.local -name "__pycache__" -type d -exec rm -rf {} + || true

# Production stage
FROM python:3.11-slim

# Set enhanced environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONHASHSEED=random
ENV PIP_NO_CACHE_DIR=1
ENV PORT=8080
ENV ENVIRONMENT=production
ENV WORKERS=1
ENV MAX_RETRIES=3
ENV REQUEST_TIMEOUT=30
ENV RATE_LIMIT_WINDOW=60
ENV MAX_REQUESTS_PER_WINDOW=100
ENV MAX_CONCURRENT_PROCESSING=10

# Install security updates and runtime dependencies
RUN apt-get update && apt-get upgrade -y \
    && apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security with specific UID/GID
RUN groupadd -r -g 1001 appuser && useradd -r -g appuser -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage with proper ownership
COPY --from=builder --chown=appuser:appuser /root/.local /home/<USER>/.local

# Copy application files with proper ownership
COPY --chown=appuser:appuser pubsub_handler.py .
COPY --chown=appuser:appuser .env.pubsub .env

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/tmp \
    && chown -R appuser:appuser /app \
    && chmod 755 /app \
    && chmod 755 /app/logs \
    && chmod 755 /app/tmp

# Switch to non-root user
USER appuser

# Make sure scripts in .local are usable
ENV PATH=/home/<USER>/.local/bin:$PATH

# Enhanced health check with better error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD python -c "\
import sys, requests, json; \
try: \
    response = requests.get('http://localhost:8080/health', timeout=8); \
    data = response.json(); \
    sys.exit(0 if data.get('status') in ['healthy', 'degraded'] else 1) \
except Exception as e: \
    print(f'Health check failed: {e}'); \
    sys.exit(1)"

# Expose port
EXPOSE 8080

# Enhanced startup command with better configuration
CMD ["uvicorn", "pubsub_handler:app", \
     "--host", "0.0.0.0", \
     "--port", "8080", \
     "--workers", "1", \
     "--access-log", \
     "--log-level", "info", \
     "--timeout-keep-alive", "30", \
     "--timeout-graceful-shutdown", "10"]
