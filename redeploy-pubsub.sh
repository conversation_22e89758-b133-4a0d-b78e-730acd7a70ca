#!/bin/bash

# Quick redeploy script for Pub/Sub handler with fixes

echo "🔧 Redeploying Pub/Sub Handler with Validation Fixes"
echo "===================================================="

PROJECT_ID="ai-email-bot-455814"
REGION="us-central1"

echo "📋 Step 1: Build and Push Updated Image"
echo "======================================="

# Build, tag, and push the updated Pub/Sub handler
docker build -f Dockerfile.pubsub -t pubsub . && \
docker tag pubsub gcr.io/$PROJECT_ID/pubsub:latest && \
docker push gcr.io/$PROJECT_ID/pubsub:latest

if [ $? -eq 0 ]; then
    echo "✅ Image built and pushed successfully!"
else
    echo "❌ Failed to build/push image"
    exit 1
fi

echo ""
echo "📋 Step 2: Deploy Updated Service"
echo "================================="

# Deploy the updated service
gcloud run deploy pubsub \
  --image gcr.io/$PROJECT_ID/pubsub:latest \
  --platform managed \
  --region $REGION \
  --project $PROJECT_ID \
  --memory 2Gi \
  --cpu 1 \
  --allow-unauthenticated \
  --concurrency 50 \
  --max-instances 5 \
  --timeout 60 \
  --set-env-vars="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,PUBSUB_TOPIC=projects/$PROJECT_ID/topics/email-notifications,ENVIRONMENT=production,PORT=8080,SERVICE_TOKEN=K8mN2pQ4rS6tU8vW0xY2zA4bC6dE8fG0hI2jK4lM6nO8,MAIN_FASTAPI_URL=https://emailbot-524036921514.us-central1.run.app"

if [ $? -eq 0 ]; then
    echo "✅ Service deployed successfully!"
else
    echo "❌ Failed to deploy service"
    exit 1
fi

# Get the service URL
PUBSUB_URL=$(gcloud run services describe pubsub --region=$REGION --project=$PROJECT_ID --format='value(status.url)')
echo "📍 Pub/Sub Handler URL: $PUBSUB_URL"

echo ""
echo "📋 Step 3: Test the Updated Service"
echo "==================================="

echo "🧪 Testing health endpoint..."
if curl -f -s "$PUBSUB_URL/health" > /dev/null; then
    echo "✅ Health check passed"
    curl -s "$PUBSUB_URL/health" | jq '.'
else
    echo "⚠️  Health check failed"
fi

echo ""
echo "📋 Step 4: Check Recent Logs"
echo "============================"

echo "📊 Recent logs from the service:"
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=pubsub" \
    --limit=10 \
    --format="table(timestamp,severity,textPayload)" \
    --project=$PROJECT_ID

echo ""
echo "🎉 Redeployment Complete!"
echo "========================"
echo ""
echo "📊 Service Details:"
echo "URL: $PUBSUB_URL"
echo "Health: $PUBSUB_URL/health"
echo "Webhook: $PUBSUB_URL/gmail-webhook"
echo ""
echo "🔧 Key Fixes Applied:"
echo "- Fixed Pydantic validation for historyId (int -> string conversion)"
echo "- Enhanced ValidationError handling with detailed logging"
echo "- Improved error messages for debugging"
echo ""
echo "📋 Next Steps:"
echo "1. Monitor logs: gcloud logs tail --service=pubsub"
echo "2. Test with a Gmail notification"
echo "3. Check if validation errors are resolved"
echo ""
echo "✅ Your Pub/Sub handler should now handle historyId validation correctly!"
