#!/usr/bin/env python3
"""
Enhanced Email Analyzer System Setup Script

This script sets up and validates the robust two-container email analyzer system
with comprehensive error handling, monitoring, and performance optimizations.
"""

import os
import sys
import json
import time
import secrets
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

def print_header(title: str):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step: str):
    """Print a formatted step"""
    print(f"\n🔧 {step}")

def print_success(message: str):
    """Print a success message"""
    print(f"✅ {message}")

def print_warning(message: str):
    """Print a warning message"""
    print(f"⚠️  {message}")

def print_error(message: str):
    """Print an error message"""
    print(f"❌ {message}")

def generate_secure_token() -> str:
    """Generate a secure service token"""
    return secrets.token_urlsafe(32)

def validate_file_exists(file_path: str, description: str) -> bool:
    """Validate that a required file exists"""
    if os.path.exists(file_path):
        print_success(f"{description} found: {file_path}")
        return True
    else:
        print_error(f"{description} not found: {file_path}")
        return False

def create_env_file(template_path: str, target_path: str, replacements: Dict[str, str]) -> bool:
    """Create environment file from template with replacements"""
    try:
        if os.path.exists(target_path):
            print_warning(f"Environment file already exists: {target_path}")
            response = input("Do you want to overwrite it? (y/N): ").lower()
            if response != 'y':
                return True

        with open(template_path, 'r') as template_file:
            content = template_file.read()

        for placeholder, value in replacements.items():
            content = content.replace(placeholder, value)

        with open(target_path, 'w') as target_file:
            target_file.write(content)

        print_success(f"Created environment file: {target_path}")
        return True
    except Exception as e:
        print_error(f"Failed to create environment file {target_path}: {str(e)}")
        return False

def validate_python_version() -> bool:
    """Validate Python version"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print_success(f"Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print_error(f"Python 3.8+ required, found: {version.major}.{version.minor}.{version.micro}")
        return False

def check_docker_installation() -> bool:
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print_success(f"Docker installed: {result.stdout.strip()}")
            
            # Check if Docker daemon is running
            result = subprocess.run(['docker', 'info'], capture_output=True, text=True)
            if result.returncode == 0:
                print_success("Docker daemon is running")
                return True
            else:
                print_error("Docker daemon is not running")
                return False
        else:
            print_error("Docker is not installed")
            return False
    except FileNotFoundError:
        print_error("Docker is not installed or not in PATH")
        return False

def validate_gcp_credentials() -> bool:
    """Validate Google Cloud Platform credentials"""
    firebase_cred_path = "backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json"
    gmail_cred_path = "backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json"
    
    firebase_valid = validate_file_exists(firebase_cred_path, "Firebase credentials")
    gmail_valid = validate_file_exists(gmail_cred_path, "Gmail API credentials")
    
    return firebase_valid and gmail_valid

def setup_environment_files() -> bool:
    """Set up environment configuration files"""
    print_step("Setting up environment configuration files")
    
    # Generate secure service token
    service_token = generate_secure_token()
    print_success(f"Generated secure service token")
    
    # Common replacements
    replacements = {
        'your-secure-service-token-here': service_token,
        'your-project-id': 'ai-email-bot-455814',
        'dev-service-token-12345': service_token
    }
    
    # Create pubsub environment file
    pubsub_success = create_env_file(
        '.env.pubsub.example',
        '.env.pubsub',
        replacements
    )
    
    # Create backend environment file
    backend_success = create_env_file(
        'backend/.env.example',
        'backend/.env',
        replacements
    )
    
    return pubsub_success and backend_success

def install_dependencies() -> bool:
    """Install Python dependencies"""
    print_step("Installing Python dependencies")
    
    try:
        # Install pubsub handler dependencies
        print("Installing Pub/Sub handler dependencies...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements-pubsub.txt'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print_error(f"Failed to install pubsub dependencies: {result.stderr}")
            return False
        
        print_success("Pub/Sub handler dependencies installed")
        
        # Install backend dependencies
        print("Installing backend dependencies...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'backend/requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print_error(f"Failed to install backend dependencies: {result.stderr}")
            return False
        
        print_success("Backend dependencies installed")
        return True
        
    except Exception as e:
        print_error(f"Error installing dependencies: {str(e)}")
        return False

def build_docker_images() -> bool:
    """Build Docker images"""
    print_step("Building Docker images")
    
    try:
        # Build pubsub handler image
        print("Building Pub/Sub handler image...")
        result = subprocess.run([
            'docker', 'build', '-f', 'Dockerfile.pubsub', '-t', 'email-analyzer-pubsub:latest', '.'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print_error(f"Failed to build pubsub image: {result.stderr}")
            return False
        
        print_success("Pub/Sub handler image built successfully")
        
        # Build backend image
        print("Building backend image...")
        result = subprocess.run([
            'docker', 'build', '-f', 'backend/Dockerfile', '-t', 'email-analyzer-backend:latest', 'backend/'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print_error(f"Failed to build backend image: {result.stderr}")
            return False
        
        print_success("Backend image built successfully")
        return True
        
    except Exception as e:
        print_error(f"Error building Docker images: {str(e)}")
        return False

def create_docker_compose() -> bool:
    """Create Docker Compose configuration"""
    print_step("Creating Docker Compose configuration")
    
    compose_content = """version: '3.8'

services:
  backend:
    image: email-analyzer-backend:latest
    ports:
      - "8000:8080"
    environment:
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped

  pubsub-handler:
    image: email-analyzer-pubsub:latest
    ports:
      - "8081:8080"
    environment:
      - ENVIRONMENT=production
      - MAIN_FASTAPI_URL=http://backend:8080
    volumes:
      - .:/app
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    restart: unless-stopped

networks:
  default:
    driver: bridge
"""
    
    try:
        with open('docker-compose.yml', 'w') as f:
            f.write(compose_content)
        print_success("Docker Compose configuration created")
        return True
    except Exception as e:
        print_error(f"Failed to create Docker Compose configuration: {str(e)}")
        return False

def main():
    """Main setup function"""
    print_header("Enhanced Email Analyzer System Setup")
    
    print("This script will set up your robust two-container email analyzer system")
    print("with enhanced error handling, monitoring, and performance optimizations.")
    
    # Validation steps
    validations = [
        ("Python version", validate_python_version),
        ("Docker installation", check_docker_installation),
        ("GCP credentials", validate_gcp_credentials),
    ]
    
    print_header("System Validation")
    for name, validator in validations:
        print_step(f"Validating {name}")
        if not validator():
            print_error(f"Validation failed: {name}")
            sys.exit(1)
    
    # Setup steps
    setup_steps = [
        ("Environment files", setup_environment_files),
        ("Python dependencies", install_dependencies),
        ("Docker images", build_docker_images),
        ("Docker Compose", create_docker_compose),
    ]
    
    print_header("System Setup")
    for name, setup_func in setup_steps:
        print_step(f"Setting up {name}")
        if not setup_func():
            print_error(f"Setup failed: {name}")
            sys.exit(1)
    
    print_header("Setup Complete!")
    print("✅ Your enhanced email analyzer system is ready!")
    print("\nNext steps:")
    print("1. Update .env.pubsub and backend/.env with your actual API keys")
    print("2. Start the system: docker-compose up -d")
    print("3. Check health: curl http://localhost:8000/health && curl http://localhost:8081/health")
    print("4. Monitor logs: docker-compose logs -f")
    
    print("\nSystem URLs:")
    print("- Backend API: http://localhost:8000")
    print("- Pub/Sub Handler: http://localhost:8081")
    print("- Backend Health: http://localhost:8000/health")
    print("- Pub/Sub Health: http://localhost:8081/health")
    print("- Backend Metrics: http://localhost:8000/metrics")
    print("- Pub/Sub Metrics: http://localhost:8081/metrics")

if __name__ == "__main__":
    main()
