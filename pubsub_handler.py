#!/usr/bin/env python3
"""
Enhanced FastAPI Gmail Push Notification Handler

This FastAPI service handles Gmail push notifications from Google Cloud Pub/Sub,
processes them, and triggers email analysis via the main FastAPI backend.

Enhanced Features:
- Robust error handling and retry mechanisms
- Rate limiting and traffic management
- Circuit breaker pattern for external API calls
- Comprehensive logging and monitoring
- Health checks and metrics collection
- Graceful degradation and fallback mechanisms

Architecture:
1. Receives POST requests from Pub/Sub on /gmail-webhook
2. Parses base64-encoded messages containing Gmail notifications
3. Extracts emailAddress and historyId from notifications
4. Queries Firestore to find user by email address
5. Uses Gmail API to fetch new emails since historyId
6. Makes HTTP POST calls to main FastAPI /emails/process-single endpoint
"""

import os
import json
import base64
import logging
import traceback
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from collections import defaultdict, deque
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request, BackgroundTasks, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
import requests
import httpx
from google.cloud import firestore
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request as GoogleRequest
from googleapiclient.errors import HttpError

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/app/logs/pubsub_handler.log') if os.path.exists('/app/logs') else logging.NullHandler()
    ]
)
logger = logging.getLogger("gmail_push_handler")

# Enhanced Configuration from environment variables with Cloud Run optimizations
MAIN_FASTAPI_URL = os.getenv('MAIN_FASTAPI_URL', 'http://localhost:8000')
SERVICE_TOKEN = os.getenv('SERVICE_TOKEN', 'dev-service-token')
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
PUBSUB_TOPIC = os.getenv('PUBSUB_TOPIC', 'projects/ai-email-bot-455814/topics/email-notifications')
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

# Cloud Run specific configuration
PORT = int(os.getenv('PORT', '8080'))  # Cloud Run sets PORT automatically
IS_CLOUD_RUN = os.getenv('K_SERVICE') is not None  # Cloud Run environment detection
CLOUD_RUN_SERVICE = os.getenv('K_SERVICE', 'pubsub-handler')
CLOUD_RUN_REVISION = os.getenv('K_REVISION', 'unknown')

# Enhanced Performance and reliability settings
MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
RETRY_DELAY = float(os.getenv('RETRY_DELAY', '1.0'))  # seconds
REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))  # seconds
MAX_EMAILS_PER_NOTIFICATION = int(os.getenv('MAX_EMAILS_PER_NOTIFICATION', '10'))

# Rate limiting settings
RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', '60'))  # seconds
MAX_REQUESTS_PER_WINDOW = int(os.getenv('MAX_REQUESTS_PER_WINDOW', '100'))
MAX_CONCURRENT_PROCESSING = int(os.getenv('MAX_CONCURRENT_PROCESSING', '10'))

# Circuit breaker settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD = int(os.getenv('CIRCUIT_BREAKER_FAILURE_THRESHOLD', '5'))
CIRCUIT_BREAKER_RECOVERY_TIMEOUT = int(os.getenv('CIRCUIT_BREAKER_RECOVERY_TIMEOUT', '60'))
CIRCUIT_BREAKER_EXPECTED_EXCEPTION = (requests.exceptions.RequestException, httpx.RequestError)

# Health check settings
HEALTH_CHECK_INTERVAL = int(os.getenv('HEALTH_CHECK_INTERVAL', '30'))  # seconds
FIRESTORE_HEALTH_CHECK_TIMEOUT = int(os.getenv('FIRESTORE_HEALTH_CHECK_TIMEOUT', '5'))

# Set log level
logger.setLevel(getattr(logging, LOG_LEVEL.upper()))

# Rate limiting storage
request_tracker = defaultdict(deque)
processing_semaphore = asyncio.Semaphore(MAX_CONCURRENT_PROCESSING)

# Circuit Breaker Implementation
class CircuitBreaker:
    """Circuit breaker pattern implementation for external API calls"""

    def __init__(self, failure_threshold: int, recovery_timeout: int):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info("Circuit breaker transitioning to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN - service unavailable")

        try:
            result = func(*args, **kwargs)
            if self.state == 'HALF_OPEN':
                self.reset()
            return result
        except CIRCUIT_BREAKER_EXPECTED_EXCEPTION as e:
            self.record_failure()
            raise e

    def record_failure(self):
        """Record a failure and potentially open the circuit"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'
            logger.warning(f"Circuit breaker OPENED after {self.failure_count} failures")

    def reset(self):
        """Reset the circuit breaker to closed state"""
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'
        logger.info("Circuit breaker RESET to CLOSED state")

# Rate Limiter Implementation
class RateLimiter:
    """Rate limiter for controlling request frequency"""

    @staticmethod
    def is_allowed(identifier: str) -> bool:
        """Check if request is allowed based on rate limiting"""
        current_time = time.time()

        # Clean old requests outside the window
        while (request_tracker[identifier] and
               current_time - request_tracker[identifier][0] > RATE_LIMIT_WINDOW):
            request_tracker[identifier].popleft()

        # Check if under limit
        if len(request_tracker[identifier]) < MAX_REQUESTS_PER_WINDOW:
            request_tracker[identifier].append(current_time)
            return True

        return False

# Initialize circuit breaker for main FastAPI calls
main_api_circuit_breaker = CircuitBreaker(
    failure_threshold=CIRCUIT_BREAKER_FAILURE_THRESHOLD,
    recovery_timeout=CIRCUIT_BREAKER_RECOVERY_TIMEOUT
)

# Initialize FastAPI app with enhanced configuration
app = FastAPI(
    title="Enhanced Gmail Push Notification Handler",
    description="Robust FastAPI service for handling Gmail push notifications from Pub/Sub",
    version="2.0.0",
    docs_url="/docs" if os.getenv('ENVIRONMENT', 'production') == 'development' else None,
    redoc_url="/redoc" if os.getenv('ENVIRONMENT', 'production') == 'development' else None
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if os.getenv('ENVIRONMENT') == 'development' else [],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Enhanced Firestore client initialization with retry logic
def initialize_firestore_client(max_retries: int = 3) -> Optional[firestore.Client]:
    """Initialize Firestore client with retry logic and better error handling"""
    for attempt in range(max_retries):
        try:
            credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS') or 'backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json'

            if os.path.exists(credentials_path):
                from google.oauth2 import service_account
                credentials = service_account.Credentials.from_service_account_file(credentials_path)
                client = firestore.Client(credentials=credentials, project=GOOGLE_CLOUD_PROJECT)

                # Test the connection
                test_ref = client.collection('_health_check').document('test')
                test_ref.set({'timestamp': firestore.SERVER_TIMESTAMP, 'service': 'pubsub_handler'})

                logger.info(f"Firestore client initialized successfully with credentials from {credentials_path}")
                return client
            else:
                # Fall back to default credentials
                client = firestore.Client(project=GOOGLE_CLOUD_PROJECT)

                # Test the connection
                test_ref = client.collection('_health_check').document('test')
                test_ref.set({'timestamp': firestore.SERVER_TIMESTAMP, 'service': 'pubsub_handler'})

                logger.info("Firestore client initialized successfully with default credentials")
                return client

        except Exception as e:
            logger.warning(f"Firestore initialization attempt {attempt + 1}/{max_retries} failed: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                logger.error(f"Failed to initialize Firestore client after {max_retries} attempts: {str(e)}")
                logger.warning("Running without Firestore connection - some features will be disabled")
                return None

# Initialize Firestore client
db = initialize_firestore_client()

# Enhanced Pydantic models with validation
class PubSubMessage(BaseModel):
    """Enhanced Pub/Sub message structure with validation"""
    message: Dict[str, Any] = Field(..., description="Pub/Sub message payload")
    subscription: Optional[str] = Field(None, description="Subscription name")

    class Config:
        extra = "allow"  # Allow additional fields

class GmailNotification(BaseModel):
    """Enhanced Gmail notification data with validation"""
    emailAddress: str = Field(..., min_length=5, max_length=254, description="Gmail email address")
    historyId: str = Field(..., min_length=1, description="Gmail history ID")

    @validator('emailAddress')
    def validate_email(cls, v):
        """Basic email validation"""
        if '@' not in v or '.' not in v.split('@')[-1]:
            raise ValueError('Invalid email address format')
        return v.lower()

    @validator('historyId')
    def validate_history_id(cls, v):
        """Validate history ID is numeric"""
        if not v.isdigit():
            raise ValueError('History ID must be numeric')
        return v

class TestNotificationRequest(BaseModel):
    """Enhanced test notification request with validation"""
    emailAddress: str = Field(..., min_length=5, max_length=254, description="Gmail email address")
    historyId: str = Field(..., min_length=1, description="Gmail history ID")

    @validator('emailAddress')
    def validate_email(cls, v):
        """Basic email validation"""
        if '@' not in v or '.' not in v.split('@')[-1]:
            raise ValueError('Invalid email address format')
        return v.lower()

class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service health status")
    timestamp: str = Field(..., description="Health check timestamp")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    firestore: Optional[str] = Field(None, description="Firestore connection status")
    main_fastapi: Optional[str] = Field(None, description="Main FastAPI connection status")
    circuit_breaker_state: Optional[str] = Field(None, description="Circuit breaker state")
    uptime_seconds: Optional[float] = Field(None, description="Service uptime in seconds")

class EnhancedGmailNotificationProcessor:
    """Enhanced Gmail push notification processor with robust error handling"""

    def __init__(self):
        self.db = db
        self.main_fastapi_url = MAIN_FASTAPI_URL
        self.service_token = SERVICE_TOKEN
        self.http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(REQUEST_TIMEOUT),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        self.metrics = {
            'total_notifications': 0,
            'successful_notifications': 0,
            'failed_notifications': 0,
            'emails_processed': 0,
            'api_calls_made': 0,
            'api_call_failures': 0,
            'last_error': None,
            'last_success': None
        }

    async def parse_pubsub_message(self, pubsub_data: Dict[str, Any]) -> Optional[GmailNotification]:
        """Enhanced Pub/Sub message parsing with better error handling"""
        try:
            self.metrics['total_notifications'] += 1

            # Validate input data structure
            if not isinstance(pubsub_data, dict):
                logger.error(f"Invalid pubsub_data type: {type(pubsub_data)}")
                return None

            # Extract message from Pub/Sub format
            message = pubsub_data.get('message', {})
            if not message:
                logger.error("No message found in Pub/Sub request")
                logger.debug(f"Received pubsub_data: {pubsub_data}")
                return None

            # Decode base64 data with enhanced error handling
            data = message.get('data', '')
            if not data:
                logger.error("No data found in Pub/Sub message")
                logger.debug(f"Message structure: {message}")
                return None

            # Decode base64 message with multiple fallback attempts
            try:
                # Primary decoding attempt
                decoded_data = base64.b64decode(data).decode('utf-8')
                notification_data = json.loads(decoded_data)
                logger.info(f"Successfully decoded notification data: {notification_data}")

                # Validate notification data structure
                if not isinstance(notification_data, dict):
                    logger.error(f"Invalid notification data type: {type(notification_data)}")
                    return None

                # Create and validate GmailNotification object
                notification = GmailNotification(**notification_data)
                logger.info(f"Created valid notification for {notification.emailAddress}")
                return notification

            except base64.binascii.Error as b64_error:
                logger.error(f"Base64 decoding error: {str(b64_error)}")
                logger.debug(f"Raw data: {data[:100]}...")  # Log first 100 chars
                return None
            except json.JSONDecodeError as json_error:
                logger.error(f"JSON parsing error: {str(json_error)}")
                logger.debug(f"Decoded data: {decoded_data[:200]}...")  # Log first 200 chars
                return None
            except ValueError as val_error:
                logger.error(f"Validation error creating GmailNotification: {str(val_error)}")
                logger.debug(f"Notification data: {notification_data}")
                return None
            except Exception as decode_error:
                logger.error(f"Unexpected error during message decoding: {str(decode_error)}")
                logger.debug(f"Error type: {type(decode_error)}")
                return None

        except Exception as e:
            logger.error(f"Critical error parsing Pub/Sub message: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def get_user_by_email(self, email_address: str) -> Optional[Dict[str, Any]]:
        """Find user in Firestore by email address in email_accounts collection"""
        try:
            if not self.db:
                logger.error("Firestore client not available")
                return None
                
            logger.info(f"Searching for user with email: {email_address}")
            
            # Query all users and their email accounts
            users_ref = self.db.collection('users')
            users = users_ref.stream()
            
            for user_doc in users:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                # Check email_accounts subcollection
                email_accounts_ref = users_ref.document(user_id).collection('email_accounts')
                accounts = email_accounts_ref.where('email', '==', email_address).stream()
                
                for account_doc in accounts:
                    account_data = account_doc.to_dict()
                    logger.info(f"Found user {user_id} with email account {account_doc.id}")
                    
                    return {
                        'user_id': user_id,
                        'user_data': user_data,
                        'account_id': account_doc.id,
                        'account_data': account_data,
                        'email_address': email_address
                    }
            
            logger.warning(f"No user found with email address: {email_address}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding user by email: {str(e)}")
            return None

    def refresh_credentials_if_needed(self, credentials_dict: Dict[str, Any]) -> Optional[Credentials]:
        """Refresh Gmail API credentials if needed"""
        try:
            credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=credentials_dict.get('scopes', [])
            )

            # Check if credentials need refresh
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("Refreshing expired credentials")
                    credentials.refresh(GoogleRequest())
                    return credentials
                else:
                    logger.error("Credentials are invalid and cannot be refreshed")
                    return None

            return credentials

        except Exception as e:
            logger.error(f"Error refreshing credentials: {str(e)}")
            return None

    def fetch_new_emails(self, user_data: Dict[str, Any], history_id: str) -> List[Dict[str, Any]]:
        """Fetch new emails from Gmail API since the given history ID"""
        try:
            user_id = user_data['user_id']
            account_id = user_data['account_id']
            account_data = user_data['account_data']

            # Get credentials from account data
            credentials_dict = account_data.get('credentials', {})
            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return []

            # Refresh credentials if needed
            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for account {account_id}")
                return []

            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)

            # Get history since the given history ID
            logger.info(f"Fetching history since {history_id} for user {user_id}")

            try:
                history_response = service.users().history().list(
                    userId='me',
                    startHistoryId=history_id,
                    historyTypes=['messageAdded'],
                    labelId='INBOX'
                ).execute()

                history_records = history_response.get('history', [])
                logger.info(f"Found {len(history_records)} history records")

                new_emails = []
                for record in history_records:
                    messages_added = record.get('messagesAdded', [])
                    for message_added in messages_added:
                        message = message_added.get('message', {})
                        email_id = message.get('id')

                        if email_id:
                            # Check if message is in INBOX
                            label_ids = message.get('labelIds', [])
                            if 'INBOX' in label_ids:
                                new_emails.append({
                                    'email_id': email_id,
                                    'user_id': user_id,
                                    'account_id': account_id,
                                    'thread_id': message.get('threadId'),
                                    'label_ids': label_ids
                                })
                                logger.info(f"Found new email: {email_id}")

                                # Limit the number of emails processed per notification
                                if len(new_emails) >= MAX_EMAILS_PER_NOTIFICATION:
                                    logger.info(f"Reached maximum emails per notification ({MAX_EMAILS_PER_NOTIFICATION}), stopping")
                                    break

                    # Break outer loop too if limit reached
                    if len(new_emails) >= MAX_EMAILS_PER_NOTIFICATION:
                        break

                logger.info(f"Total new emails found: {len(new_emails)}")
                return new_emails

            except Exception as history_error:
                logger.error(f"Error fetching Gmail history: {str(history_error)}")
                return []

        except Exception as e:
            logger.error(f"Error fetching new emails: {str(e)}")
            return []

    async def trigger_email_analysis(self, email_id: str, user_id: str, account_id: str) -> bool:
        """Enhanced email analysis trigger with circuit breaker and async support"""

        def make_api_call():
            """Internal function for circuit breaker"""
            payload = {
                'email_id': email_id,
                'user_id': user_id,
                'account_id': account_id,
                'priority': 'real_time',
                'source': 'gmail_push_notification',
                'timestamp': datetime.now().isoformat()
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.service_token}',
                'X-Service-Source': 'pubsub-handler',
                'X-Request-ID': f"{user_id}_{email_id}_{int(time.time())}"
            }

            url = f"{self.main_fastapi_url}/emails/process-single"

            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=REQUEST_TIMEOUT
            )

            self.metrics['api_calls_made'] += 1

            if response.status_code == 200:
                logger.info(f"Successfully triggered analysis for email {email_id}")
                self.metrics['last_success'] = datetime.now().isoformat()
                return True
            else:
                error_msg = f"API call failed. Status: {response.status_code}, Response: {response.text[:200]}"
                logger.error(error_msg)
                self.metrics['api_call_failures'] += 1
                self.metrics['last_error'] = error_msg
                raise requests.exceptions.RequestException(error_msg)

        # Retry logic with exponential backoff
        for attempt in range(MAX_RETRIES):
            try:
                logger.info(f"Triggering email analysis (attempt {attempt + 1}/{MAX_RETRIES}) for email {email_id}")

                # Use circuit breaker for API calls
                result = main_api_circuit_breaker.call(make_api_call)
                return result

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"Attempt {attempt + 1}/{MAX_RETRIES} failed: {error_msg}")

                if attempt < MAX_RETRIES - 1:
                    # Exponential backoff with jitter
                    delay = RETRY_DELAY * (2 ** attempt) + (time.time() % 1)  # Add jitter
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"All {MAX_RETRIES} attempts failed for email {email_id}")
                    self.metrics['api_call_failures'] += 1
                    self.metrics['last_error'] = error_msg
                    return False

        return False

    async def process_gmail_notification(self, notification: GmailNotification) -> Dict[str, Any]:
        """Process individual Gmail notification"""
        try:
            logger.info(f"Processing notification for {notification.emailAddress}, historyId: {notification.historyId}")

            # Find user by email address
            user_data = self.get_user_by_email(notification.emailAddress)
            if not user_data:
                logger.warning(f"No user found for email address: {notification.emailAddress}")
                return {
                    'success': False,
                    'error': f'No user found for email address: {notification.emailAddress}'
                }

            # Fetch new emails since history ID
            new_emails = self.fetch_new_emails(user_data, notification.historyId)

            if not new_emails:
                logger.info(f"No new emails found for {notification.emailAddress}")
                return {
                    'success': True,
                    'message': 'No new emails to process',
                    'emails_processed': 0
                }

            # Trigger analysis for each new email
            successful_triggers = 0
            failed_triggers = 0

            for email_info in new_emails:
                success = self.trigger_email_analysis(
                    email_info['email_id'],
                    email_info['user_id'],
                    email_info['account_id']
                )

                if success:
                    successful_triggers += 1
                else:
                    failed_triggers += 1

            logger.info(f"Processed {len(new_emails)} emails. Success: {successful_triggers}, Failed: {failed_triggers}")

            return {
                'success': True,
                'message': f'Processed {len(new_emails)} emails',
                'emails_processed': len(new_emails),
                'successful_triggers': successful_triggers,
                'failed_triggers': failed_triggers
            }

        except Exception as e:
            logger.error(f"Error processing Gmail notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# Initialize the enhanced processor
processor = EnhancedGmailNotificationProcessor()

# Enhanced metrics tracking
service_metrics = {
    'notifications_received': 0,
    'notifications_processed': 0,
    'notifications_failed': 0,
    'emails_triggered': 0,
    'emails_failed': 0,
    'last_notification_time': None,
    'service_start_time': datetime.now().isoformat(),
    'rate_limited_requests': 0,
    'circuit_breaker_trips': 0,
    'average_processing_time': 0.0,
    'processing_times': deque(maxlen=100),  # Keep last 100 processing times
    'cold_starts': 0,
    'warm_starts': 0
}

# Cloud Run startup optimization
@app.on_event("startup")
async def startup_event():
    """Startup event handler for Cloud Run optimizations"""
    logger.info(f"🚀 Starting Enhanced Gmail Push Notification Handler v2.0.0")
    logger.info(f"Environment: {'Cloud Run' if IS_CLOUD_RUN else 'Local'}")
    logger.info(f"Port: {PORT}")
    logger.info(f"Main FastAPI URL: {MAIN_FASTAPI_URL}")

    if IS_CLOUD_RUN:
        logger.info(f"Cloud Run Service: {CLOUD_RUN_SERVICE}")
        logger.info(f"Cloud Run Revision: {CLOUD_RUN_REVISION}")
        service_metrics['cold_starts'] += 1
    else:
        service_metrics['warm_starts'] += 1

    # Pre-warm connections for faster response times
    try:
        if db:
            # Test Firestore connection
            test_ref = db.collection('_startup_test').document('test')
            test_ref.set({'timestamp': firestore.SERVER_TIMESTAMP, 'service': 'pubsub_handler'})
            logger.info("✅ Firestore connection pre-warmed")

        # Pre-warm HTTP client
        async with httpx.AsyncClient(timeout=5.0) as client:
            try:
                response = await client.get(f"{MAIN_FASTAPI_URL}/health")
                if response.status_code == 200:
                    logger.info("✅ Main FastAPI connection pre-warmed")
                else:
                    logger.warning(f"⚠️ Main FastAPI responded with status {response.status_code}")
            except Exception as e:
                logger.warning(f"⚠️ Could not pre-warm main FastAPI connection: {str(e)}")

    except Exception as e:
        logger.warning(f"⚠️ Startup pre-warming failed: {str(e)}")

    logger.info("🎉 Startup complete - ready to handle requests")

# FastAPI endpoints
@app.get("/")
async def root():
    """Enhanced root endpoint with Cloud Run information"""
    return {
        "service": "Enhanced Gmail Push Notification Handler",
        "version": "2.0.0",
        "status": "running",
        "framework": "FastAPI",
        "architecture": "event_driven_pubsub",
        "environment": "cloud_run" if IS_CLOUD_RUN else "local",
        "cloud_run_service": CLOUD_RUN_SERVICE if IS_CLOUD_RUN else None,
        "cloud_run_revision": CLOUD_RUN_REVISION if IS_CLOUD_RUN else None,
        "port": PORT,
        "main_fastapi_url": MAIN_FASTAPI_URL,
        "endpoints": {
            "health": "/health",
            "webhook": "/gmail-webhook",
            "test": "/test-notification",
            "metrics": "/metrics"
        }
    }

@app.get("/metrics")
async def get_metrics():
    """Get enhanced service metrics"""
    uptime_seconds = (datetime.now() - datetime.fromisoformat(service_metrics['service_start_time'])).total_seconds()

    # Calculate average processing time
    if service_metrics['processing_times']:
        avg_processing_time = sum(service_metrics['processing_times']) / len(service_metrics['processing_times'])
        service_metrics['average_processing_time'] = avg_processing_time

    return {
        "service": "enhanced-gmail-push-handler",
        "version": "2.0.0",
        "metrics": {
            **service_metrics,
            "processor_metrics": processor.metrics,
            "circuit_breaker_state": main_api_circuit_breaker.state,
            "circuit_breaker_failures": main_api_circuit_breaker.failure_count
        },
        "uptime_seconds": uptime_seconds,
        "uptime_human": f"{int(uptime_seconds // 3600)}h {int((uptime_seconds % 3600) // 60)}m {int(uptime_seconds % 60)}s"
    }

@app.get("/health")
async def enhanced_health_check():
    """Enhanced health check endpoint with comprehensive monitoring"""
    health_start_time = time.time()

    try:
        uptime_seconds = (datetime.now() - datetime.fromisoformat(service_metrics['service_start_time'])).total_seconds()

        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'enhanced-gmail-push-handler',
            'version': '2.0.0',
            'framework': 'FastAPI',
            'uptime_seconds': uptime_seconds,
            'uptime_human': f"{int(uptime_seconds // 3600)}h {int((uptime_seconds % 3600) // 60)}m {int(uptime_seconds % 60)}s"
        }

        # Check Firestore connection with timeout
        firestore_check_start = time.time()
        if db:
            try:
                test_ref = db.collection('_health_check').document('pubsub_handler_health')
                test_ref.set({
                    'timestamp': firestore.SERVER_TIMESTAMP,
                    'service': 'pubsub_handler',
                    'version': '2.0.0'
                })
                firestore_response_time = time.time() - firestore_check_start
                health_status['firestore'] = {
                    'status': 'connected',
                    'response_time_ms': round(firestore_response_time * 1000, 2)
                }
            except Exception as db_error:
                firestore_response_time = time.time() - firestore_check_start
                error_msg = str(db_error)[:100]  # Limit error message length
                health_status['firestore'] = {
                    'status': 'error',
                    'error': error_msg,
                    'response_time_ms': round(firestore_response_time * 1000, 2)
                }
                health_status['status'] = 'degraded'
        else:
            health_status['firestore'] = {
                'status': 'not_initialized',
                'error': 'Firestore client not available'
            }
            health_status['status'] = 'degraded'

        # Check main FastAPI connectivity with enhanced monitoring
        api_check_start = time.time()
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{MAIN_FASTAPI_URL}/health")
                api_response_time = time.time() - api_check_start

                if response.status_code == 200:
                    health_status['main_fastapi'] = {
                        'status': 'connected',
                        'response_time_ms': round(api_response_time * 1000, 2),
                        'status_code': response.status_code
                    }
                else:
                    health_status['main_fastapi'] = {
                        'status': 'degraded',
                        'response_time_ms': round(api_response_time * 1000, 2),
                        'status_code': response.status_code
                    }
                    health_status['status'] = 'degraded'
        except Exception as api_error:
            api_response_time = time.time() - api_check_start
            error_msg = str(api_error)[:100]  # Limit error message length
            health_status['main_fastapi'] = {
                'status': 'error',
                'error': error_msg,
                'response_time_ms': round(api_response_time * 1000, 2)
            }
            health_status['status'] = 'degraded'

        # Add circuit breaker status
        health_status['circuit_breaker'] = {
            'state': main_api_circuit_breaker.state,
            'failure_count': main_api_circuit_breaker.failure_count,
            'last_failure_time': main_api_circuit_breaker.last_failure_time
        }

        # Add performance metrics
        health_status['performance'] = {
            'notifications_received': service_metrics['notifications_received'],
            'notifications_processed': service_metrics['notifications_processed'],
            'notifications_failed': service_metrics['notifications_failed'],
            'success_rate': (
                service_metrics['notifications_processed'] / max(service_metrics['notifications_received'], 1) * 100
                if service_metrics['notifications_received'] > 0 else 0
            ),
            'average_processing_time_ms': round(service_metrics.get('average_processing_time', 0) * 1000, 2)
        }

        # Add resource usage
        health_status['resources'] = {
            'concurrent_processing_slots_used': MAX_CONCURRENT_PROCESSING - processing_semaphore._value,
            'max_concurrent_processing': MAX_CONCURRENT_PROCESSING,
            'rate_limited_requests': service_metrics['rate_limited_requests']
        }

        # Overall health check response time
        health_check_time = time.time() - health_start_time
        health_status['health_check_time_ms'] = round(health_check_time * 1000, 2)

        status_code = 200 if health_status['status'] == 'healthy' else 503
        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        health_check_time = time.time() - health_start_time
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            content={
                'status': 'unhealthy',
                'error': str(e)[:200],  # Limit error message length
                'timestamp': datetime.now().isoformat(),
                'health_check_time_ms': round(health_check_time * 1000, 2)
            },
            status_code=503
        )

@app.post("/gmail-webhook")
async def handle_gmail_notification_enhanced(request: Request, background_tasks: BackgroundTasks):
    """Enhanced Gmail push notification handler with rate limiting and robust error handling"""
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"

    try:
        # Rate limiting check
        if not RateLimiter.is_allowed(client_ip):
            service_metrics['rate_limited_requests'] += 1
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Update metrics
        service_metrics['notifications_received'] += 1
        service_metrics['last_notification_time'] = datetime.now().isoformat()

        logger.info(f"Received Gmail push notification from {client_ip}")

        # Get request data with size validation
        try:
            request_data = await request.json()

            # Validate request size (prevent DoS)
            request_size = len(json.dumps(request_data))
            if request_size > 1024 * 1024:  # 1MB limit
                logger.warning(f"Request too large: {request_size} bytes")
                raise HTTPException(status_code=413, detail="Request too large")

            logger.debug(f"Request data size: {request_size} bytes")

        except json.JSONDecodeError as json_err:
            logger.error(f"Invalid JSON in request: {str(json_err)}")
            service_metrics['notifications_failed'] += 1
            raise HTTPException(status_code=400, detail="Invalid JSON format")

        # Parse Pub/Sub message with enhanced error handling
        notification = await processor.parse_pubsub_message(request_data)
        if not notification:
            logger.error("Failed to parse Pub/Sub message")
            service_metrics['notifications_failed'] += 1
            raise HTTPException(status_code=400, detail="Failed to parse Pub/Sub message")

        # Check concurrent processing limit
        if processing_semaphore.locked():
            logger.warning("Maximum concurrent processing reached, queuing request")

        # Process the notification in background with semaphore control
        background_tasks.add_task(
            process_notification_enhanced_background,
            notification,
            start_time
        )

        # Return immediate response to Pub/Sub
        processing_time = time.time() - start_time
        return {
            "success": True,
            "message": "Notification received and processing started",
            "emailAddress": notification.emailAddress,
            "historyId": notification.historyId,
            "processing_time_ms": round(processing_time * 1000, 2),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException as e:
        service_metrics['notifications_failed'] += 1
        processing_time = time.time() - start_time
        logger.error(f"HTTP error handling notification: {e.detail} (took {processing_time:.3f}s)")
        raise e
    except Exception as e:
        service_metrics['notifications_failed'] += 1
        processing_time = time.time() - start_time
        error_msg = str(e)
        logger.error(f"Critical error handling Gmail notification: {error_msg} (took {processing_time:.3f}s)")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {error_msg}")

@app.post("/test-notification")
async def test_notification(test_request: TestNotificationRequest):
    """Test endpoint for simulating Gmail notifications (development only)"""
    try:
        # Only allow in development
        if os.getenv('ENVIRONMENT', 'development') != 'development':
            raise HTTPException(status_code=403, detail="Test endpoint only available in development")

        logger.info(f"Processing test notification for {test_request.emailAddress}")

        # Create notification object
        notification = GmailNotification(
            emailAddress=test_request.emailAddress,
            historyId=test_request.historyId
        )

        # Process the notification
        result = await processor.process_gmail_notification(notification)

        return {
            "test_mode": True,
            "result": result
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in test notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")

async def process_notification_enhanced_background(notification: GmailNotification, start_time: float):
    """Enhanced background task to process Gmail notification with semaphore control"""
    async with processing_semaphore:  # Limit concurrent processing
        processing_start = time.time()
        try:
            logger.info(f"Starting enhanced background processing for notification: {notification.emailAddress}")

            # Process the notification
            result = await processor.process_gmail_notification(notification)

            # Calculate processing time
            total_processing_time = time.time() - start_time
            background_processing_time = time.time() - processing_start

            # Update metrics based on result
            if result.get('success'):
                service_metrics['notifications_processed'] += 1
                service_metrics['emails_triggered'] += result.get('emails_processed', 0)
                logger.info(f"Successfully processed notification for {notification.emailAddress}")
            else:
                service_metrics['notifications_failed'] += 1
                logger.warning(f"Failed to process notification for {notification.emailAddress}: {result.get('error', 'Unknown error')}")

            # Track processing times for metrics
            service_metrics['processing_times'].append(total_processing_time)

            logger.info(f"Background processing completed in {background_processing_time:.3f}s (total: {total_processing_time:.3f}s)")
            logger.debug(f"Processing result: {result}")

        except Exception as e:
            service_metrics['notifications_failed'] += 1
            processing_time = time.time() - processing_start
            error_msg = str(e)
            logger.error(f"Critical error in background notification processing: {error_msg} (took {processing_time:.3f}s)")
            logger.error(f"Notification details: {notification.emailAddress}, {notification.historyId}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Optionally implement dead letter queue or retry mechanism here
            # For now, we'll just log the failure

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not found",
            "message": "The requested endpoint does not exist"
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    import uvicorn

    # Configuration for local development
    port = int(os.getenv('PORT', 8081))  # Changed default port to 8081
    host = os.getenv('HOST', '0.0.0.0')
    debug = os.getenv('ENVIRONMENT', 'development') == 'development'

    logger.info(f"Starting Gmail Push Handler on {host}:{port}")
    logger.info(f"Main FastAPI URL: {MAIN_FASTAPI_URL}")
    logger.info(f"Debug mode: {debug}")

    uvicorn.run(
        "pubsub_handler:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
