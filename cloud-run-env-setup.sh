#!/bin/bash

# Cloud Run Environment Variables Setup Script
# This script configures environment variables and secrets for Cloud Run deployment

set -e

PROJECT_ID=${1:-$(gcloud config get-value project)}
REGION=${2:-"us-central1"}

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: PROJECT_ID is required"
    echo "Usage: $0 <PROJECT_ID> [REGION]"
    exit 1
fi

echo "🔧 Setting up Cloud Run environment variables"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"

# Function to update secret
update_secret() {
    local secret_name=$1
    local file_path=$2
    local description=$3
    
    if [ -f "$file_path" ]; then
        echo "📝 Updating $description..."
        gcloud secrets versions add $secret_name \
            --data-file="$file_path" \
            --project=$PROJECT_ID
        echo "✅ Updated $secret_name"
    else
        echo "⚠️  File not found: $file_path - skipping $description"
    fi
}

# Function to update secret from string
update_secret_string() {
    local secret_name=$1
    local secret_value=$2
    local description=$3
    
    if [ -n "$secret_value" ] && [ "$secret_value" != "PLACEHOLDER" ]; then
        echo "📝 Updating $description..."
        echo -n "$secret_value" | gcloud secrets versions add $secret_name \
            --data-file=- \
            --project=$PROJECT_ID
        echo "✅ Updated $secret_name"
    else
        echo "⚠️  No value provided for $description - skipping"
    fi
}

# Update secrets with real credentials
echo "🔒 Updating Secret Manager secrets..."

# Firebase credentials
update_secret "firebase-credentials" \
    "backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json" \
    "Firebase credentials"

# Gmail credentials
update_secret "gmail-credentials" \
    "backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json" \
    "Gmail API credentials"

# Gemini API key (prompt user if not provided)
if [ -z "$GEMINI_API_KEY" ]; then
    echo "🔑 Please enter your Gemini API key (or press Enter to skip):"
    read -s GEMINI_API_KEY
fi

update_secret_string "gemini-api-key" \
    "$GEMINI_API_KEY" \
    "Gemini API key"

# Get service URLs after deployment
echo "🌐 Getting service URLs..."

# Check if services are deployed
BACKEND_URL=""
PUBSUB_URL=""

if gcloud run services describe email-analyzer-backend --region=$REGION --project=$PROJECT_ID >/dev/null 2>&1; then
    BACKEND_URL=$(gcloud run services describe email-analyzer-backend \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format='value(status.url)')
    echo "✅ Backend URL: $BACKEND_URL"
else
    echo "⚠️  Backend service not deployed yet"
fi

if gcloud run services describe email-analyzer-pubsub --region=$REGION --project=$PROJECT_ID >/dev/null 2>&1; then
    PUBSUB_URL=$(gcloud run services describe email-analyzer-pubsub \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format='value(status.url)')
    echo "✅ Pub/Sub URL: $PUBSUB_URL"
else
    echo "⚠️  Pub/Sub service not deployed yet"
fi

# Update Pub/Sub handler with backend URL if both services exist
if [ -n "$BACKEND_URL" ] && [ -n "$PUBSUB_URL" ]; then
    echo "🔗 Updating Pub/Sub handler with backend URL..."
    gcloud run services update email-analyzer-pubsub \
        --region=$REGION \
        --project=$PROJECT_ID \
        --set-env-vars="MAIN_FASTAPI_URL=$BACKEND_URL"
    echo "✅ Updated Pub/Sub handler configuration"
fi

# Configure Pub/Sub push subscription
if [ -n "$PUBSUB_URL" ]; then
    echo "📬 Configuring Pub/Sub push subscription..."
    
    WEBHOOK_URL="$PUBSUB_URL/gmail-webhook"
    
    # Check if subscription exists
    if gcloud pubsub subscriptions describe email-notifications-push --project=$PROJECT_ID >/dev/null 2>&1; then
        echo "📝 Updating existing subscription..."
        gcloud pubsub subscriptions modify-push-config email-notifications-push \
            --push-endpoint="$WEBHOOK_URL" \
            --project=$PROJECT_ID
    else
        echo "📝 Creating new push subscription..."
        gcloud pubsub subscriptions create email-notifications-push \
            --topic=email-notifications \
            --push-endpoint="$WEBHOOK_URL" \
            --ack-deadline=60 \
            --project=$PROJECT_ID
    fi
    
    echo "✅ Pub/Sub subscription configured: $WEBHOOK_URL"
fi

# Create environment configuration file for local development
echo "📄 Creating local environment configuration..."

cat > .env.cloudrun << EOF
# Cloud Run Environment Configuration
# Generated on $(date)

# Project Configuration
GOOGLE_CLOUD_PROJECT=$PROJECT_ID
REGION=$REGION

# Service URLs
BACKEND_URL=$BACKEND_URL
PUBSUB_URL=$PUBSUB_URL

# Pub/Sub Configuration
PUBSUB_TOPIC=projects/$PROJECT_ID/topics/email-notifications
WEBHOOK_URL=$PUBSUB_URL/gmail-webhook

# Service Accounts
BACKEND_SA=email-analyzer-backend-sa@$PROJECT_ID.iam.gserviceaccount.com
PUBSUB_SA=email-analyzer-pubsub-sa@$PROJECT_ID.iam.gserviceaccount.com

# Secrets
SERVICE_TOKEN_SECRET=email-analyzer-service-token
FIREBASE_CREDENTIALS_SECRET=firebase-credentials
GMAIL_CREDENTIALS_SECRET=gmail-credentials
GEMINI_API_KEY_SECRET=gemini-api-key
EOF

echo "✅ Created .env.cloudrun configuration file"

# Display deployment status
echo ""
echo "📊 Deployment Status:"
echo "===================="

if [ -n "$BACKEND_URL" ]; then
    echo "✅ Backend API: $BACKEND_URL"
    echo "   Health: $BACKEND_URL/health"
    echo "   Metrics: $BACKEND_URL/metrics"
else
    echo "❌ Backend API: Not deployed"
fi

if [ -n "$PUBSUB_URL" ]; then
    echo "✅ Pub/Sub Handler: $PUBSUB_URL"
    echo "   Health: $PUBSUB_URL/health"
    echo "   Webhook: $PUBSUB_URL/gmail-webhook"
else
    echo "❌ Pub/Sub Handler: Not deployed"
fi

echo ""
echo "🔒 Secrets Status:"
echo "=================="
gcloud secrets list --filter="name:email-analyzer" --format="table(name,createTime)" --project=$PROJECT_ID

echo ""
echo "📬 Pub/Sub Status:"
echo "=================="
if gcloud pubsub topics describe email-notifications --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "✅ Topic: email-notifications"
else
    echo "❌ Topic: email-notifications (not found)"
fi

if gcloud pubsub subscriptions describe email-notifications-push --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "✅ Subscription: email-notifications-push"
    PUSH_ENDPOINT=$(gcloud pubsub subscriptions describe email-notifications-push --project=$PROJECT_ID --format='value(pushConfig.pushEndpoint)')
    echo "   Endpoint: $PUSH_ENDPOINT"
else
    echo "❌ Subscription: email-notifications-push (not found)"
fi

echo ""
echo "🎉 Environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Deploy services: gcloud builds submit --config=cloudbuild.yaml"
echo "2. Test health endpoints"
echo "3. Configure Gmail push notifications"
echo "4. Test email processing"
