#!/usr/bin/env python3
"""
Integrated Email Analyzer with Built-in Pub/Sub Handler
Single container approach combining main backend and Pub/Sub handler
"""

import os
import logging
import asyncio
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import firebase_admin
from firebase_admin import credentials, firestore
from dotenv import load_dotenv
import uvicorn

# Import existing routers
from backend.routers import gmail_router, gemini_router, auth_router, batch_email_router

# Import Pub/Sub handler components
import json
import base64
import traceback
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import requests
from google.cloud import firestore as firestore_client
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request as GoogleRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("integrated_main")

# Load environment variables
load_dotenv()

# Configuration
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
PUBSUB_TOPIC = os.getenv('PUBSUB_TOPIC', 'projects/ai-email-bot-455814/topics/email-notifications')
SERVICE_TOKEN = os.getenv('SERVICE_TOKEN', 'dev-service-token')

# Initialize Firebase Admin SDK
try:
    cred_path = os.getenv("FIREBASE_CREDENTIALS_PATH", "backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json")
    if os.path.exists(cred_path):
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(cred, {
            'storageBucket': os.getenv("FIREBASE_STORAGE_BUCKET", "ai-email-bot-455814.appspot.com")
        })
        logger.info("Firebase Admin SDK initialized successfully")
    else:
        logger.warning(f"Firebase credentials file not found at {cred_path}")
except Exception as e:
    logger.error(f"Failed to initialize Firebase Admin SDK: {str(e)}")

# Initialize FastAPI app
app = FastAPI(
    title="Integrated Email Analyzer API",
    description="Combined Email Analyzer API with built-in Pub/Sub handler",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pub/Sub Handler Models
class PubSubMessage(BaseModel):
    """Pub/Sub message structure"""
    message: Dict[str, Any]
    subscription: Optional[str] = None

class GmailNotification(BaseModel):
    """Gmail notification data"""
    emailAddress: str
    historyId: str

# Integrated Gmail Notification Processor
class IntegratedGmailProcessor:
    """Handles Gmail push notification processing within the main app"""
    
    def __init__(self):
        self.db = firestore.client()
        
    def parse_pubsub_message(self, pubsub_data: Dict[str, Any]) -> Optional[GmailNotification]:
        """Parse Pub/Sub message and extract Gmail notification data"""
        try:
            message = pubsub_data.get('message', {})
            if not message:
                logger.error("No message found in Pub/Sub request")
                return None
                
            data = message.get('data', '')
            if not data:
                logger.error("No data found in Pub/Sub message")
                return None
                
            decoded_data = base64.b64decode(data).decode('utf-8')
            notification_data = json.loads(decoded_data)
            logger.info(f"Decoded notification data: {notification_data}")
            
            return GmailNotification(**notification_data)
        except Exception as e:
            logger.error(f"Error parsing Pub/Sub message: {str(e)}")
            return None
    
    def get_user_by_email(self, email_address: str) -> Optional[Dict[str, Any]]:
        """Find user in Firestore by email address"""
        try:
            logger.info(f"Searching for user with email: {email_address}")
            
            users_ref = self.db.collection('users')
            users = users_ref.stream()
            
            for user_doc in users:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                email_accounts_ref = users_ref.document(user_id).collection('email_accounts')
                accounts = email_accounts_ref.where('email', '==', email_address).stream()
                
                for account_doc in accounts:
                    account_data = account_doc.to_dict()
                    logger.info(f"Found user {user_id} with email account {account_doc.id}")
                    
                    return {
                        'user_id': user_id,
                        'user_data': user_data,
                        'account_id': account_doc.id,
                        'account_data': account_data,
                        'email_address': email_address
                    }
            
            logger.warning(f"No user found with email address: {email_address}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding user by email: {str(e)}")
            return None

    def refresh_credentials_if_needed(self, credentials_dict: Dict[str, Any]) -> Optional[Credentials]:
        """Refresh Gmail API credentials if needed"""
        try:
            credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=credentials_dict.get('scopes', [])
            )

            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("Refreshing expired credentials")
                    credentials.refresh(GoogleRequest())
                    return credentials
                else:
                    logger.error("Credentials are invalid and cannot be refreshed")
                    return None

            return credentials

        except Exception as e:
            logger.error(f"Error refreshing credentials: {str(e)}")
            return None

    def fetch_new_emails(self, user_data: Dict[str, Any], history_id: str) -> List[Dict[str, Any]]:
        """Fetch new emails from Gmail API since the given history ID"""
        try:
            user_id = user_data['user_id']
            account_id = user_data['account_id']
            account_data = user_data['account_data']

            credentials_dict = account_data.get('credentials', {})
            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return []

            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for account {account_id}")
                return []

            service = build('gmail', 'v1', credentials=credentials)

            logger.info(f"Fetching history since {history_id} for user {user_id}")

            try:
                history_response = service.users().history().list(
                    userId='me',
                    startHistoryId=history_id,
                    historyTypes=['messageAdded'],
                    labelId='INBOX'
                ).execute()

                history_records = history_response.get('history', [])
                logger.info(f"Found {len(history_records)} history records")

                new_emails = []
                for record in history_records:
                    messages_added = record.get('messagesAdded', [])
                    for message_added in messages_added:
                        message = message_added.get('message', {})
                        email_id = message.get('id')

                        if email_id:
                            label_ids = message.get('labelIds', [])
                            if 'INBOX' in label_ids:
                                new_emails.append({
                                    'email_id': email_id,
                                    'user_id': user_id,
                                    'account_id': account_id,
                                    'thread_id': message.get('threadId'),
                                    'label_ids': label_ids
                                })
                                logger.info(f"Found new email: {email_id}")

                                if len(new_emails) >= 10:  # Limit emails per notification
                                    break

                    if len(new_emails) >= 10:
                        break

                logger.info(f"Total new emails found: {len(new_emails)}")
                return new_emails

            except Exception as history_error:
                logger.error(f"Error fetching Gmail history: {str(history_error)}")
                return []

        except Exception as e:
            logger.error(f"Error fetching new emails: {str(e)}")
            return []

    async def process_gmail_notification(self, notification: GmailNotification) -> Dict[str, Any]:
        """Process Gmail notification and trigger email analysis directly"""
        try:
            logger.info(f"Processing notification for {notification.emailAddress}, historyId: {notification.historyId}")

            # Find user by email address
            user_data = self.get_user_by_email(notification.emailAddress)
            if not user_data:
                logger.warning(f"No user found for email address: {notification.emailAddress}")
                return {
                    'success': False,
                    'error': f'No user found for email address: {notification.emailAddress}'
                }

            # Fetch new emails since history ID
            new_emails = self.fetch_new_emails(user_data, notification.historyId)

            if not new_emails:
                logger.info(f"No new emails found for {notification.emailAddress}")
                return {
                    'success': True,
                    'message': 'No new emails to process',
                    'emails_processed': 0
                }

            # Process emails directly using the batch email router's background function
            successful_triggers = 0
            failed_triggers = 0

            for email_info in new_emails:
                try:
                    # Call the background processing function directly
                    await batch_email_router.process_single_email_background(
                        email_info['email_id'],
                        email_info['user_id'],
                        email_info['account_id'],
                        'gmail_push_notification'
                    )
                    successful_triggers += 1
                    logger.info(f"Successfully processed email {email_info['email_id']}")
                except Exception as e:
                    failed_triggers += 1
                    logger.error(f"Failed to process email {email_info['email_id']}: {str(e)}")

            logger.info(f"Processed {len(new_emails)} emails. Success: {successful_triggers}, Failed: {failed_triggers}")

            return {
                'success': True,
                'message': f'Processed {len(new_emails)} emails',
                'emails_processed': len(new_emails),
                'successful_triggers': successful_triggers,
                'failed_triggers': failed_triggers
            }

        except Exception as e:
            logger.error(f"Error processing Gmail notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# Initialize the integrated processor
integrated_processor = IntegratedGmailProcessor()

# Include existing routers
app.include_router(auth_router.router, prefix="/auth", tags=["Authentication"])
app.include_router(gmail_router.router, prefix="/gmail", tags=["Gmail"])
app.include_router(gemini_router.router, prefix="/gemini", tags=["Gemini AI"])
app.include_router(batch_email_router.router, prefix="/emails", tags=["Email Batch Operations"])

# Add integrated Pub/Sub endpoint
@app.post("/gmail-webhook")
async def handle_gmail_notification_integrated(request: Request, background_tasks: BackgroundTasks):
    """Handle Gmail push notifications directly in the main app"""
    try:
        logger.info("Received Gmail push notification (integrated)")

        request_data = await request.json()
        logger.info(f"Request data: {request_data}")

        # Parse Pub/Sub message
        notification = integrated_processor.parse_pubsub_message(request_data)
        if not notification:
            logger.error("Failed to parse Pub/Sub message")
            raise HTTPException(status_code=400, detail="Failed to parse Pub/Sub message")

        # Process the notification in background
        background_tasks.add_task(
            process_notification_integrated_background,
            notification
        )

        return {
            "success": True,
            "message": "Notification received and processing started (integrated)",
            "emailAddress": notification.emailAddress,
            "historyId": notification.historyId
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error handling Gmail notification: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

async def process_notification_integrated_background(notification: GmailNotification):
    """Background task to process Gmail notification in integrated mode"""
    try:
        logger.info(f"Starting integrated background processing for notification: {notification.emailAddress}")
        result = await integrated_processor.process_gmail_notification(notification)
        logger.info(f"Integrated background processing completed: {result}")
    except Exception as e:
        logger.error(f"Error in integrated background notification processing: {str(e)}")

@app.get("/")
async def root():
    return {
        "message": "Welcome to Integrated Email Analyzer API",
        "architecture": "single_container_with_integrated_pubsub",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "Integrated Email Analyzer API is running",
        "timestamp": datetime.now().isoformat(),
        "architecture": "integrated_pubsub"
    }

if __name__ == "__main__":
    uvicorn.run("integrated_main:app", host="0.0.0.0", port=8080, reload=True)
