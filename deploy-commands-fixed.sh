#!/bin/bash

# Fixed deployment commands for your email analyzer system

echo "🚀 Deploying Email Analyzer System to Cloud Run"
echo "================================================"

# Set your project ID
PROJECT_ID="ai-email-bot-455814"
REGION="us-central1"

echo "📋 Step 1: Deploy Pub/Sub Handler"
echo "=================================="

# Build, tag, push and deploy Pub/Sub handler
docker build -f Dockerfile.pubsub -t pubsub . && \
docker tag pubsub gcr.io/$PROJECT_ID/pubsub:latest && \
docker push gcr.io/$PROJECT_ID/pubsub:latest && \
gcloud run deploy pubsub \
  --image gcr.io/$PROJECT_ID/pubsub:latest \
  --platform managed \
  --region $REGION \
  --project $PROJECT_ID \
  --memory 2Gi \
  --cpu 1 \
  --allow-unauthenticated \
  --concurrency 50 \
  --max-instances 5 \
  --timeout 60 \
  --set-env-vars="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,PUBSUB_TOPIC=projects/$PROJECT_ID/topics/email-notifications,ENVIRONMENT=production,PORT=8080"

echo "✅ Pub/Sub Handler deployed successfully!"

# Get the Pub/Sub handler URL
PUBSUB_URL=$(gcloud run services describe pubsub --region=$REGION --project=$PROJECT_ID --format='value(status.url)')
echo "📍 Pub/Sub Handler URL: $PUBSUB_URL"

echo ""
echo "📋 Step 2: Deploy Backend API"
echo "=============================="

# Build, tag, push and deploy Backend API
docker build -f Dockerfile.backend -t emailbot . && \
docker tag emailbot gcr.io/$PROJECT_ID/emailbot:latest && \
docker push gcr.io/$PROJECT_ID/emailbot:latest && \
gcloud run deploy emailbot \
  --image gcr.io/$PROJECT_ID/emailbot:latest \
  --platform managed \
  --region $REGION \
  --project $PROJECT_ID \
  --memory 2Gi \
  --cpu 1 \
  --allow-unauthenticated \
  --concurrency 50 \
  --max-instances 10 \
  --timeout 300 \
  --set-env-vars="ENVIRONMENT=production,PORT=8080"

echo "✅ Backend API deployed successfully!"

# Get the Backend API URL
BACKEND_URL=$(gcloud run services describe emailbot --region=$REGION --project=$PROJECT_ID --format='value(status.url)')
echo "📍 Backend API URL: $BACKEND_URL"

echo ""
echo "📋 Step 3: Update Pub/Sub Handler with Backend URL"
echo "=================================================="

# Update Pub/Sub handler with the backend URL
gcloud run services update pubsub \
  --region=$REGION \
  --project=$PROJECT_ID \
  --set-env-vars="MAIN_FASTAPI_URL=$BACKEND_URL,GOOGLE_CLOUD_PROJECT=$PROJECT_ID,PUBSUB_TOPIC=projects/$PROJECT_ID/topics/email-notifications,ENVIRONMENT=production,PORT=8080"

echo "✅ Pub/Sub Handler updated with Backend URL!"

echo ""
echo "📋 Step 4: Configure Pub/Sub Topic and Subscription"
echo "=================================================="

# Create Pub/Sub topic if it doesn't exist
if ! gcloud pubsub topics describe email-notifications --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud pubsub topics create email-notifications --project=$PROJECT_ID
    echo "✅ Created Pub/Sub topic: email-notifications"
else
    echo "ℹ️  Pub/Sub topic already exists"
fi

# Create or update push subscription
WEBHOOK_URL="$PUBSUB_URL/gmail-webhook"

if gcloud pubsub subscriptions describe email-notifications-push --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "📝 Updating existing subscription..."
    gcloud pubsub subscriptions modify-push-config email-notifications-push \
        --push-endpoint="$WEBHOOK_URL" \
        --project=$PROJECT_ID
else
    echo "📝 Creating new push subscription..."
    gcloud pubsub subscriptions create email-notifications-push \
        --topic=email-notifications \
        --push-endpoint="$WEBHOOK_URL" \
        --ack-deadline=60 \
        --project=$PROJECT_ID
fi

echo "✅ Pub/Sub subscription configured!"

echo ""
echo "📋 Step 5: Test Deployments"
echo "============================"

echo "🧪 Testing Backend API health..."
if curl -f -s "$BACKEND_URL/health" > /dev/null; then
    echo "✅ Backend API health check passed"
else
    echo "⚠️  Backend API health check failed"
fi

echo "🧪 Testing Pub/Sub Handler health..."
if curl -f -s "$PUBSUB_URL/health" > /dev/null; then
    echo "✅ Pub/Sub Handler health check passed"
else
    echo "⚠️  Pub/Sub Handler health check failed"
fi

echo ""
echo "🎉 Deployment Complete!"
echo "======================="
echo ""
echo "📊 Service URLs:"
echo "Backend API: $BACKEND_URL"
echo "  Health: $BACKEND_URL/health"
echo "  Docs: $BACKEND_URL/docs"
echo ""
echo "Pub/Sub Handler: $PUBSUB_URL"
echo "  Health: $PUBSUB_URL/health"
echo "  Webhook: $PUBSUB_URL/gmail-webhook"
echo ""
echo "📬 Pub/Sub Configuration:"
echo "Topic: projects/$PROJECT_ID/topics/email-notifications"
echo "Subscription: email-notifications-push"
echo "Webhook URL: $WEBHOOK_URL"
echo ""
echo "📋 Next Steps:"
echo "1. Configure Gmail push notifications to use: $WEBHOOK_URL"
echo "2. Test email processing by sending a test email"
echo "3. Monitor logs: gcloud logs tail --service=pubsub && gcloud logs tail --service=emailbot"
echo ""
echo "✅ Your email analyzer system is now running on Cloud Run!"
