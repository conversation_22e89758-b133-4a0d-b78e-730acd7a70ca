from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import asyncio
import json
import time
import re
import traceback
from datetime import datetime, timed<PERSON>ta
from firebase_admin import firestore
from firebase_admin import storage
from routers import auth_router
from routers.auth_router import verify_token
from routers.gmail_router import get_gmail_service, get_message_details, search_messages, get_raw_email_data
from routers.gemini_router import analyze_with_gemini_direct
import email_storage
import email_scheduler
import os
import base64
import tempfile
import logging

# Import the webhook sender
from .webhook_sender import send_to_webhook

# Import the Flanker email parser
from email_parser import is_valid_attachment_id

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("batch_email")

router = APIRouter()

# Enhanced model definitions with better validation
class BatchEmailRequest(BaseModel):
    email_ids: List[str]
    account_id: str = Field(description="Account ID to use for fetching emails")
    analyze: Optional[bool] = Field(default=True, description="Whether to analyze emails after fetching")

class EmailRetrievalConfig(BaseModel):
    account_id: str
    interval: int = Field(ge=1, le=1440, description="Interval in minutes (1-1440)")
    enabled: bool
    filter: Optional[str] = None
    max_results: Optional[int] = Field(default=50, ge=1, le=100, description="Maximum results per run (1-100)")

class BatchAnalyzeRequest(BaseModel):
    query: Optional[str] = None
    max_results: Optional[int] = Field(default=20, ge=1, le=100, description="Maximum results to return")
    date: Optional[str] = None  # Format: YYYY-MM-DD
    start_date: Optional[str] = None  # Format: YYYY-MM-DD
    end_date: Optional[str] = None  # Format: YYYY-MM-DD
    use_last_retrieval_time: Optional[bool] = Field(default=True, description="Use last retrieval time to optimize fetching")
    account_id: Optional[str] = None  # Account ID for retrieving emails from specific account
    analyze_only_new: Optional[bool] = Field(default=True, description="Only analyze emails that haven't been analyzed before")
    immediate_processing: Optional[bool] = Field(default=False, description="Whether to mark this request for immediate processing by the background processor")

class RunNowRequest(BaseModel):
    config_id: str
    immediate_processing: Optional[bool] = Field(default=False, description="Whether to mark this config for immediate processing by the background processor")

class HistoricalEmailRequest(BaseModel):
    """Request model for historical email analysis"""
    time_period: str = Field(..., description="Time period to analyze: 'last_week', 'last_month', 'last_3_months', 'last_year', or 'custom'")
    start_date: Optional[str] = None  # Format: YYYY-MM-DD, used if time_period is 'custom'
    end_date: Optional[str] = None    # Format: YYYY-MM-DD, used if time_period is 'custom'
    max_results: Optional[int] = Field(default=50, ge=1, le=200, description="Maximum results to return")
    account_id: Optional[str] = None  # Account ID for retrieving emails from specific account
    category_filter: Optional[str] = None  # Filter for specific email categories: 'orders', 'finance', 'travel', etc.

class SingleEmailProcessRequest(BaseModel):
    """Request model for processing single emails from Cloud Run"""
    email_id: str = Field(..., description="Gmail message ID to process")
    user_id: str = Field(..., description="User ID who owns the email account")
    account_id: str = Field(..., description="Account ID for the email account")
    priority: Optional[str] = Field(default="real_time", description="Processing priority")
    source: Optional[str] = Field(default="gmail_push_notification", description="Source of the processing request")

# Rate limiting configuration
MAX_BATCH_SIZE = 20  # Maximum number of emails to process in a single batch
RATE_LIMIT_WINDOW = 60  # 60 seconds window
MAX_REQUESTS_PER_WINDOW = 5  # Maximum 5 requests per minute per user

# Dictionary to track requests per user for rate limiting
user_request_tracker = {}



def fix_category_mismatch(analysis_result, email_data, email_id):
    """
    Fix category mismatch when Gemini correctly identifies document type 
    but sets category to 'other'
    """
    try:
        # Get current category
        category = analysis_result.get('category', 'other')
        
        # If category is already correct, no need to fix
        if category in ['purchase_order', 'invoice', 'order_confirmation']:
            return analysis_result, category
            
        # Check document info for type (this is where Gemini got it right)
        doc_info = analysis_result.get('document_info', {})
        doc_type = doc_info.get('type', '').lower()
        
        # Fix category based on document type
        if 'purchase order' in doc_type:
            analysis_result['category'] = 'purchase_order'
            category = 'purchase_order'
            logger.info(f"CATEGORY FIX: Email {email_id} - Changed from 'other' to 'purchase_order' based on document type: {doc_type}")
            
        elif 'invoice' in doc_type:
            analysis_result['category'] = 'invoice'  
            category = 'invoice'
            logger.info(f"CATEGORY FIX: Email {email_id} - Changed from 'other' to 'invoice' based on document type: {doc_type}")
            
        elif 'order confirmation' in doc_type:
            analysis_result['category'] = 'order_confirmation'
            category = 'order_confirmation' 
            logger.info(f"CATEGORY FIX: Email {email_id} - Changed from 'other' to 'order_confirmation' based on document type: {doc_type}")
            
        # Also check if we have order/PO numbers (another indicator)
        po_number = doc_info.get('po_number', '')
        order_number = doc_info.get('order_number', '') 
        invoice_number = doc_info.get('invoice_number', '')
        
        if category == 'other' and (po_number or order_number):
            analysis_result['category'] = 'purchase_order'
            category = 'purchase_order'
            logger.info(f"CATEGORY FIX: Email {email_id} - Changed to 'purchase_order' based on numbers: PO:{po_number}, Order:{order_number}")
            
        elif category == 'other' and invoice_number:
            analysis_result['category'] = 'invoice'
            category = 'invoice'
            logger.info(f"CATEGORY FIX: Email {email_id} - Changed to 'invoice' based on invoice number: {invoice_number}")
            
        return analysis_result, category
        
    except Exception as e:
        logger.error(f"Error in fix_category_mismatch for email {email_id}: {str(e)}")
        return analysis_result, analysis_result.get('category', 'other')

def check_rate_limit(user_id: str) -> bool:
    """
    Check if the user has exceeded the rate limit
    Returns True if request should proceed, False if rate limited
    """
    current_time = time.time()

    if user_id not in user_request_tracker:
        user_request_tracker[user_id] = []

    # Remove requests older than the window
    user_request_tracker[user_id] = [t for t in user_request_tracker[user_id]
                                    if current_time - t < RATE_LIMIT_WINDOW]

    # Check if user has exceeded the limit
    if len(user_request_tracker[user_id]) >= MAX_REQUESTS_PER_WINDOW:
        return False

    # Add current request timestamp
    user_request_tracker[user_id].append(current_time)
    return True

async def fetch_and_store_email(service, email_id, user_id, account_id):
    """Fetch an email from Gmail and store it in Firestore with improved error handling using Flanker"""
    try:
        # Ensure email_id is a string (not a dict)
        if isinstance(email_id, dict) and 'id' in email_id:
            email_id = email_id['id']
        elif not isinstance(email_id, str):
            logger.error(f"Invalid email ID format: {type(email_id)}")
            return (email_id, {"error": f"Invalid email ID format: {type(email_id)}"})

        # First try to get the raw email data for Flanker parsing
        try:
            logger.info(f"Fetching raw data for email {email_id} for Flanker parsing")
            raw_data, _ = get_raw_email_data(service, 'me', email_id)

            # If we have raw data, add it to the email data for Flanker parsing in email_storage
            if raw_data:
                logger.info(f"Successfully retrieved raw email data for {email_id}")
        except Exception as raw_err:
            logger.warning(f"Error getting raw email data for {email_id}: {str(raw_err)}. Will use standard parsing.")
            raw_data = None

        # Get email details from Gmail with better error handling
        try:
            logger.info(f"Fetching details for email {email_id}")
            email_data = get_message_details(service, msg_id=email_id)

            # Add raw data if available for Flanker parsing in email_storage
            if raw_data:
                email_data['raw_data'] = raw_data

            logger.info(f"Successfully retrieved email data for {email_id}")
        except Exception as e:
            logger.error(f"Error in get_message_details for {email_id}: {str(e)}")
            return (email_id, {"error": f"Error getting email details: {str(e)}"})

        # Verify that email_data is a dictionary
        if not isinstance(email_data, dict):
            logger.error(f"Email data for {email_id} is not a dictionary: {type(email_data)}")
            return (email_id, {"error": f"Invalid email data type: {type(email_data)}"})

        # Add account_id to email_data for proper storage
        email_data['accountId'] = account_id

        # Store in Firestore with better error handling
        try:
            # Create a composite ID that includes the account ID to avoid duplicates
            composite_id = f"{account_id}_{email_id}"

            # Use the improved email storage module
            store_result = email_storage.store_raw_email(email_data, user_id, account_id)
            if store_result is False:
                logger.error(f"Failed to store email {email_id} in Firestore")
                return (email_id, {"error": "Failed to store email in Firestore"})
            logger.info(f"Successfully stored email {email_id} in Firestore with composite ID {composite_id}")
        except Exception as e:
            logger.error(f"Error storing email {email_id} in Firestore: {str(e)}")
            return (email_id, {"error": f"Error storing email: {str(e)}"})

        return (email_id, email_data)
    except Exception as e:
        logger.error(f"Error fetching email {email_id}: {str(e)}")
        return (email_id, {"error": str(e)})

async def process_attachments(service, email_id, attachment, user_id):
    """Process an attachment and store it in Firebase Storage"""
    try:
        # Get the attachment data from Gmail API
        attachment_data = service.users().messages().attachments().get(
            userId='me',
            messageId=email_id,
            id=attachment['id']
        ).execute()

        # Decode the attachment data
        file_data = base64.urlsafe_b64decode(attachment_data['data'])

        # Sanitize the filename
        filename = attachment['filename']
        safe_filename = ''.join(c for c in filename if c.isalnum() or c in '._- ')

        # Determine content type
        content_type = attachment.get('mimeType', 'application/octet-stream')

        # Create a temporary file for upload
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name

        try:
            # Upload to Firebase Storage
            bucket = storage.bucket()
            storage_path = f"users/{user_id}/attachments/{email_id}/{safe_filename}"
            blob = bucket.blob(storage_path)

            # Upload with content type
            blob.upload_from_filename(temp_file_path, content_type=content_type)

            # Get download URL
            url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{storage_path.replace('/', '%2F')}?alt=media"

            # Return the processed attachment data
            return {
                'id': attachment['id'],
                'filename': safe_filename,
                'content_type': content_type,
                'size': len(file_data),
                'url': url
            }
        finally:
            # Clean up temp file
            os.unlink(temp_file_path)
    except Exception as e:
        logger.error(f"Error processing attachment: {str(e)}")
        return None

@router.post("/batch")
async def get_emails_batch(
    request: BatchEmailRequest,
    background_tasks: BackgroundTasks,
    user_data: dict = Depends(verify_token)
):
    """Get multiple emails in a single request with improved handling"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Limit batch size
        if len(request.email_ids) > MAX_BATCH_SIZE:
            logger.warning(f"Batch size limited from {len(request.email_ids)} to {MAX_BATCH_SIZE}")
            request.email_ids = request.email_ids[:MAX_BATCH_SIZE]

        # Get credentials from Firestore
        db = firestore.client()
        user_id = user_data['uid']

        # Get credentials for the specified account
        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(request.account_id).get()

        if not account_doc.exists or 'credentials' not in account_doc.to_dict():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Gmail credentials not found for account {request.account_id}"
            )

        credentials = account_doc.to_dict()['credentials']

        service = get_gmail_service(credentials_dict=credentials, user_id=user_id, account_id=request.account_id)

        # First check if we already have these emails in Firestore
        emails_ref = db.collection('users').document(user_id).collection('emails')

        email_results = {}
        missing_email_ids = []

        # Check which emails we already have in Firestore, using composite IDs for lookups
        for email_id in request.email_ids:
            # Create a composite ID that includes the account ID
            composite_id = f"{request.account_id}_{email_id}"

            # Check using the composite ID
            email_doc = emails_ref.document(composite_id).get()

            if email_doc.exists and email_doc.to_dict().get('body'):
                # We have this email in Firestore
                email_results[email_id] = email_doc.to_dict()
            else:
                # We need to fetch this email from Gmail
                missing_email_ids.append(email_id)

        # Fetch missing emails from Gmail (up to 10 at a time to avoid rate limits)
        for i in range(0, len(missing_email_ids), 10):
            batch_ids = missing_email_ids[i:i+10]
            tasks = []

            for email_id in batch_ids:
                # Create a task for each email fetch
                tasks.append(asyncio.create_task(
                    fetch_and_store_email(service, email_id, user_id, request.account_id)
                ))

            # Wait for all tasks to complete
            batch_results = await asyncio.gather(*tasks)

            # Add results to the email_results dictionary
            for email_id, email_data in batch_results:
                email_results[email_id] = email_data

        # If analyze flag is true, analyze emails in the background
        if request.analyze:
            for email_id, email_data in email_results.items():
                if 'error' not in email_data:
                    # Schedule email analysis in the background to avoid blocking
                    background_tasks.add_task(
                        analyze_email_background,
                        email_id,
                        email_data,
                        user_id,
                        request.account_id
                    )

        return {"emails": list(email_results.values())}

    except Exception as e:
        logger.error(f"Error fetching emails in batch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching emails in batch: {str(e)}"
        )

async def analyze_email_background(email_id: str, email_data: dict, user_id: str, account_id: str):
    """Analyze an email in the background with clean JSON structure"""
    try:
        # Get Gemini API key
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("Gemini API key not found in environment variables")
            return

        # Create a composite ID that includes the account ID
        composite_id = f"{account_id}_{email_id}"

        # Check if this email has already been analyzed
        db = firestore.client()
        analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
        analysis_doc = analyses_ref.document(composite_id).get()

        if analysis_doc.exists and analysis_doc.to_dict().get('analysis'):
            logger.info(f"Email {email_id} already analyzed and has analysis data, skipping")
            return
        elif analysis_doc.exists:
            logger.info(f"Email {email_id} marked as analyzed but missing analysis data, will reanalyze")
        else:
            logger.info(f"Email {email_id} not yet analyzed, will analyze")

        # Get attachment data
        service = None
        attachment_contents = []

        # Only process attachments if the email has them
        if email_data.get('attachments') and len(email_data['attachments']) > 0:
            try:
                # Get the Gmail service using the account credentials
                account_doc = db.collection('users').document(user_id).collection('email_accounts').document(account_id).get()

                if account_doc.exists and 'credentials' in account_doc.to_dict():
                    credentials = account_doc.to_dict()['credentials']
                    service = get_gmail_service(credentials_dict=credentials, user_id=user_id, account_id=account_id)
                else:
                    logger.error(f"No credentials found for account {account_id}")

                if service:
                    # Process attachments with improved error handling
                    logger.info(f"Processing {len(email_data['attachments'])} attachments for email {email_id}")
                    for attachment in email_data['attachments']:
                        try:
                            # Check if we already have attachment data from Flanker
                            if 'data' in attachment:
                                logger.info(f"Using pre-loaded attachment data for {attachment.get('filename', 'unknown')} from email {email_id}")
                                attachment_contents.append({
                                    'data': attachment['data'],
                                    'filename': attachment.get('filename', 'unknown'),
                                    'content_type': attachment.get('content_type', attachment.get('mimeType', 'application/octet-stream')),
                                    'size': len(attachment['data'])
                                })
                                continue

                            # Get attachment ID with error handling
                            attachment_id = attachment.get('id')
                            if not attachment_id:
                                logger.warning(f"Missing attachment ID for attachment {attachment.get('filename', 'unknown')} in email {email_id}")
                                continue

                            # Validate attachment ID
                            if not is_valid_attachment_id(attachment_id):
                                logger.warning(f"Skipping invalid attachment ID: {attachment_id} for email {email_id}")
                                continue

                            logger.info(f"Downloading attachment {attachment.get('filename', 'unknown')} (ID: {attachment_id}) from email {email_id}")

                            try:
                                # Get attachment data directly from Gmail
                                attachment_data = service.users().messages().attachments().get(
                                    userId='me',
                                    messageId=email_id,
                                    id=attachment_id
                                ).execute()
                            except Exception as att_err:
                                logger.error(f"Error downloading attachment {attachment.get('filename', 'unknown')}: {str(att_err)}")
                                continue

                            # Check if data exists in response
                            if 'data' not in attachment_data:
                                logger.warning(f"No data found in attachment response for {attachment.get('filename', 'unknown')} in email {email_id}")
                                continue

                            # Decode the attachment data
                            file_data = base64.urlsafe_b64decode(attachment_data['data'])
                            logger.info(f"Successfully downloaded attachment {attachment.get('filename', 'unknown')} ({len(file_data)} bytes)")

                            attachment_contents.append({
                                'data': file_data,
                                'filename': attachment.get('filename', 'unknown'),
                                'content_type': attachment.get('mimeType', attachment.get('content_type', 'application/octet-stream')),
                                'size': len(file_data)
                            })
                        except Exception as e:
                            logger.error(f"Error processing attachment {attachment.get('filename', 'unknown')}: {str(e)}")
            except Exception as e:
                logger.error(f"Error getting Gmail service: {str(e)}")

        # Analyze the email with Gemini using clean structure
        try:
            logger.info(f"Analyzing email {email_id} with Gemini AI in background")
            analysis_result = analyze_with_gemini_direct(
                api_key,
                email_data,
                None,  # No attachment texts
                attachment_contents
            )


            analysis_result, category = fix_category_mismatch(analysis_result, email_data, email_id)



            logger.info(f"Successfully analyzed email {email_id} in background")
        except Exception as analysis_err:
            logger.error(f"Error analyzing email with Gemini in background: {str(analysis_err)}")
            # Provide a default analysis result to avoid breaking the flow
            analysis_result = {
                'error': True,
                'error_message': str(analysis_err),
                'analysis_results': {
                    'summary': 'Error analyzing email',
                    'sentiment': 'neutral'
                },
                'category': 'other'
            }

        # Store the analysis result
        if not analysis_result.get('error', False):
            # Store attachments in Firebase if needed
            if attachment_contents and service:
                from firebase_admin import storage
                bucket = storage.bucket()

                # Initialize attachments array if it doesn't exist
                if 'attachments' not in analysis_result:
                    analysis_result['attachments'] = []

                # Process each attachment
                for i, attachment in enumerate(attachment_contents):
                    try:
                        # Store in Firebase Storage
                        storage_path = f"users/{user_id}/attachments/{email_id}/{attachment['filename']}"
                        blob = bucket.blob(storage_path)

                        # Create temporary file for upload
                        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                            temp_file.write(attachment['data'])
                            temp_file_path = temp_file.name

                        try:
                            # Upload with content type
                            blob.upload_from_filename(temp_file_path, content_type=attachment['content_type'])

                            # Generate URL for the attachment
                            url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{storage_path.replace('/', '%2F')}?alt=media"

                            # Update or add attachment entry in analysis result
                            if i < len(analysis_result['attachments']):
                                analysis_result['attachments'][i]['url'] = url
                                analysis_result['attachments'][i]['size'] = len(attachment['data'])
                            else:
                                # Add new attachment entry
                                attachment_entry = {
                                    'filename': attachment['filename'],
                                    'content_type': attachment['content_type'],
                                    'size': len(attachment['data']),
                                    'url': url,
                                    'analysis': {
                                        'document_type': 'Document',
                                        'extracted_data_confidence': 0.98,
                                        'content_summary': f"Attachment: {attachment['filename']}"
                                    }
                                }
                                analysis_result['attachments'].append(attachment_entry)

                            # Add viewer URL for PDFs and docs
                            content_type = attachment['content_type'].lower()
                            if content_type in ['application/pdf', 'application/msword',
                                              'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                                if i < len(analysis_result['attachments']):
                                    analysis_result['attachments'][i]['viewer_url'] = f"https://docs.google.com/viewer?url={url}&embedded=true"

                            logger.info(f"Stored attachment in Firebase: {attachment['filename']} with URL: {url}")
                        finally:
                            # Clean up temp file
                            os.unlink(temp_file_path)
                    except Exception as e:
                        logger.error(f"Error storing attachment in Firebase: {str(e)}")

            # Store the email fingerprint for deduplication
            email_fingerprint = analysis_result.get('email_fingerprint', '')

            # Prepare clean analysis data for storage
            analysis_data = {
                # Basic metadata
                'created_at': firestore.SERVER_TIMESTAMP,
                'email_id': email_id,
                'composite_id': composite_id,
                'account_id': account_id,
                'user_id': user_id,

                # Email metadata from original email data
                'subject': email_data.get('subject', 'No Subject'),
                'from': email_data.get('from', 'Unknown'),
                'to': email_data.get('to', ''),
                'cc': email_data.get('cc', ''),
                'bcc': email_data.get('bcc', ''),
                'date': email_data.get('date', ''),
                'has_attachments': bool(email_data.get('attachments')),
                'email_fingerprint': email_fingerprint,

                # Store the complete clean analysis result
                'analysis': analysis_result,

                # Extract key fields for easier querying (clean structure)
                'category': analysis_result.get('category', 'other'),
                'summary': analysis_result.get('analysis_results', {}).get('summary', ''),
                'sentiment': analysis_result.get('analysis_results', {}).get('sentiment', 'neutral'),
                'total_amount': analysis_result.get('financial_details', {}).get('total_amount', ''),
                'document_number': analysis_result.get('document_info', {}).get('document_number', ''),
                'po_number': analysis_result.get('document_info', {}).get('po_number', ''),
                'invoice_number': analysis_result.get('document_info', {}).get('invoice_number', ''),
                'vendor_name': analysis_result.get('parties', {}).get('vendor', {}).get('name', ''),
                'customer_name': analysis_result.get('parties', {}).get('customer', {}).get('name', ''),

                # Key dates and action items
                'key_dates': analysis_result.get('analysis_results', {}).get('action_items', []),
                'action_items': analysis_result.get('analysis_results', {}).get('action_items', []),

                # Add timestamps for tracking
                'analyzed_at': firestore.SERVER_TIMESTAMP,
                'last_updated': firestore.SERVER_TIMESTAMP,

                # Store attachment info
                'attachments': analysis_result.get('attachments', [])
            }

            # Store in Firestore using the composite ID
            analyses_ref.document(composite_id).set(analysis_data)

            # Update the email document to indicate it has been analyzed
            email_doc_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
            email_doc_ref.update({
                'has_analysis': True,
                'analysis_timestamp': firestore.SERVER_TIMESTAMP
            })

             # Check if the email category is 'purchase_order', 'invoice', or 'order' and trigger the webhook
            webhook_categories = ['purchase_order', 'invoice', 'order', 'order_confirmation']
            category = analysis_result.get('category', 'other')

            # Enhanced webhook triggering logic
            should_trigger = False
            
            # Check 1: Category is in webhook categories
            if not analysis_result.get('error', False) and category in webhook_categories:
                should_trigger = True
                logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Category '{category}' is in webhook categories")
            
            # Check 2: Document type indicates business document (even if category is wrong)
            doc_type = analysis_result.get('document_info', {}).get('type', '').lower()
            if not analysis_result.get('error', False) and ('purchase order' in doc_type or 'invoice' in doc_type or 'order' in doc_type):
                should_trigger = True
                logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Document type '{doc_type}' indicates business document")
                
                # Also fix the category if it's wrong
                if 'purchase order' in doc_type and category == 'other':
                    analysis_result['category'] = 'purchase_order'
                    category = 'purchase_order'
                    logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Fixed category to 'purchase_order' based on document type")
            
            # Check 3: Has order/PO numbers (even if category is wrong)
            doc_info = analysis_result.get('document_info', {})
            has_business_numbers = (doc_info.get('po_number') or doc_info.get('order_number') or 
                                  doc_info.get('invoice_number') or doc_info.get('document_number'))
            
            if not analysis_result.get('error', False) and has_business_numbers:
                should_trigger = True
                logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Has business numbers: PO:{doc_info.get('po_number')}, Order:{doc_info.get('order_number')}, Invoice:{doc_info.get('invoice_number')}")
                
                # Fix category if we have PO/Order numbers but category is other
                if (doc_info.get('po_number') or doc_info.get('order_number')) and category == 'other':
                    analysis_result['category'] = 'purchase_order'
                    category = 'purchase_order'
                    logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Fixed category to 'purchase_order' based on order numbers")
            
            # Check 4: Attachment names (existing logic but enhanced)
            attachment_names = [att.get('filename', '').lower() for att in email_data.get('attachments', [])]
            po_keywords = ['purchase', 'order', 'invoice', 'po_', 'po-', 'purchaseorder']
            has_po_attachment = any(any(keyword in name for keyword in po_keywords) for name in attachment_names)

            if not analysis_result.get('error', False) and has_po_attachment:
                should_trigger = True
                logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Has purchase order attachment: {attachment_names}")
                
                if category == 'other':
                    analysis_result['category'] = 'purchase_order'
                    category = 'purchase_order'
                    logger.info(f"WEBHOOK TRIGGER: Email {email_id} - Fixed category to 'purchase_order' based on attachments")
            if should_trigger:

                # Update the stored analysis_data with the corrected category
                analysis_data['category'] = category
                analysis_result['category'] = category
                
                logger.info(f"WEBHOOK SEND: Email {email_id} qualified for webhook - Final category: '{category}', Document type: '{doc_type}', Has numbers: {bool(has_business_numbers)}")
                logger.info(f"Email {email_id} categorized as '{category}' which is in webhook categories {webhook_categories}. Triggering webhook AFTER storage.")
                try:
                    # Import webhook sender
                    from .webhook_sender import send_to_webhook

                    # Create complete webhook data structure using stored analysis_data
                    webhook_data = {
                        **analysis_data,  # Include all analysis data
                        'attachments': analysis_result.get('attachments', [])  # Ensure attachments with URLs
                    }

                    print(f"[WEBHOOK SYNC] Sending webhook SYNCHRONOUSLY for email {email_id} AFTER storage")
                    logger.info(f"Sending webhook synchronously for email {email_id} AFTER storage")

                    # Call webhook synchronously (this will block until complete)
                    webhook_success = send_to_webhook(webhook_data)

                    if webhook_success:
                        print(f"[WEBHOOK SYNC] Successfully sent webhook for email {email_id}")
                        logger.info(f"Successfully sent webhook for email {email_id}")
                    else:
                        print(f"[WEBHOOK SYNC] Failed to send webhook for email {email_id}")
                        logger.error(f"Failed to send webhook for email {email_id}")

                except Exception as webhook_err:
                    logger.error(f"Error sending webhook for email {email_id}: {str(webhook_err)}")
                    logger.error(f"WEBHOOK ERROR: Traceback: {traceback.format_exc()}")
                    print(f"[WEBHOOK SYNC ERROR] Failed to send webhook: {str(webhook_err)}")

            logger.info(f"Analysis for email {email_id} stored in Firestore with composite ID {composite_id}")

    except Exception as e:
        logger.error(f"Error analyzing email in background: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

@router.post("/config")
async def save_email_retrieval_config(
    config: EmailRetrievalConfig,
    user_data: dict = Depends(verify_token)
):
    """Save email retrieval configuration with improved validation"""
    try:
        # Log the user data for debugging
        logger.info(f"User data in save_email_retrieval_config: {user_data}")

        # Check if uid exists in user_data
        if 'uid' not in user_data:
            logger.error(f"Missing uid in user_data: {user_data}")
            # Try to use user_id if available
            if 'user_id' in user_data:
                logger.info(f"Using user_id instead of uid: {user_data['user_id']}")
                user_id = user_data['user_id']
            else:
                logger.error("No uid or user_id found in user_data")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials: missing user ID"
                )
        else:
            user_id = user_data['uid']

        logger.info(f"Saving email config for user: {user_id}")
        db = firestore.client()

        # Validate interval is within acceptable range
        if config.interval < 1 or config.interval > 1440:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Interval must be between 1 and 1440 minutes (24 hours)"
            )

        # Validate max_results is within acceptable range
        if config.max_results is not None and (config.max_results < 1 or config.max_results > 100):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="max_results must be between 1 and 100"
            )

        # Check if the account exists
        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(config.account_id).get()
        if not account_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Account {config.account_id} not found"
            )

            account_email = account_doc.to_dict().get('email', 'Unknown')
        else:
            # Check if any account exists in email_accounts collection  
            accounts_ref = db.collection('users').document(user_id).collection('email_accounts')
            accounts = accounts_ref.limit(1).get()
            if not accounts:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No Gmail accounts found. Please connect an account first."
                )

            # Use the first available account as the default
            first_account = accounts[0]
            account_email = first_account.to_dict().get('email', 'Email Account')

        # Check if a config already exists for this account
        configs_ref = db.collection('users').document(user_id).collection('email_configs')
        query = configs_ref.where('account_id', '==', config.account_id).limit(1)
        existing_configs = query.get()

        config_data = {
            'account_id': config.account_id,
            'interval': config.interval,
            'enabled': config.enabled,
            'filter': config.filter,
            'max_results': config.max_results or 50,
            'user_id': user_id,
            'account_email': account_email,
            'updated_at': firestore.SERVER_TIMESTAMP
        }

        config_id = None

        if len(existing_configs) > 0:
            # Update existing config
            config_id = existing_configs[0].id
            configs_ref.document(config_id).update(config_data)
            logger.info(f"Updated config {config_id} for account {config.account_id}")
        else:
            # Create new config
            config_data['created_at'] = firestore.SERVER_TIMESTAMP
            doc_ref = configs_ref.add(config_data)
            config_id = doc_ref[1].id
            logger.info(f"Created new config {config_id} for account {config.account_id}")

        # Schedule the job if enabled
        if config.enabled and config_id:
            config_data['id'] = config_id
            email_scheduler.schedule_job(user_id, config_data)
            logger.info(f"Scheduled job for config {config_id}")
        elif not config.enabled and config_id:
            email_scheduler.cancel_job(user_id, config_id)
            logger.info(f"Cancelled job for config {config_id}")

        return {
            "success": True,
            "message": "Email retrieval configuration saved",
            "config_id": config_id,
            "account_email": account_email
        }

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error saving email retrieval configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error saving email retrieval configuration: {str(e)}"
        )

@router.get("/configs")
async def get_email_retrieval_configs(
    user_data: dict = Depends(verify_token)
):
    """Get all email retrieval configurations for the user"""
    try:
        # Log the user data for debugging
        logger.info(f"User data in get_email_retrieval_configs: {user_data}")

        # Check if uid exists in user_data
        if 'uid' not in user_data:
            logger.error(f"Missing uid in user_data: {user_data}")
            # Try to use user_id if available
            if 'user_id' in user_data:
                logger.info(f"Using user_id instead of uid: {user_data['user_id']}")
                user_id = user_data['user_id']
            else:
                logger.error("No uid or user_id found in user_data")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials: missing user ID"
                )
        else:
            user_id = user_data['uid']

        logger.info(f"Getting email configs for user: {user_id}")
        db = firestore.client()
        configs_ref = db.collection('users').document(user_id).collection('email_configs')
        configs = configs_ref.get()

        configs_list = [{**config.to_dict(), "id": config.id} for config in configs]
        logger.info(f"Found {len(configs_list)} configs for user {user_id}")

        return {
            "configs": configs_list
        }

    except Exception as e:
        logger.error(f"Error getting email retrieval configurations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting email retrieval configurations: {str(e)}"
        )

@router.post("/retrieval/start/{config_id}")
async def start_email_retrieval(
    config_id: str,
    user_data: dict = Depends(verify_token)
):
    """Start email retrieval for a specific configuration"""
    try:
        db = firestore.client()
        user_id = user_data['uid']

        # Get the configuration
        config_ref = db.collection('users').document(user_id).collection('email_configs').document(config_id)
        config_doc = config_ref.get()

        if not config_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Configuration not found"
            )

        config = config_doc.to_dict()

        # Check if the account exists
        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(config['account_id']).get()
        if not account_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Account {config['account_id']} not found"
            )

        config['id'] = config_id

        # Enable the configuration
        config_ref.update({
            'enabled': True,
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        config['enabled'] = True

        # Schedule the job
        email_scheduler.schedule_job(user_id, config)

        # Run the job immediately once
        email_scheduler.run_job_now(user_id, config_id)

        return {"success": True, "message": "Email retrieval started"}

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error starting email retrieval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting email retrieval: {str(e)}"
        )

@router.post("/retrieval/stop/{config_id}")
async def stop_email_retrieval(
    config_id: str,
    user_data: dict = Depends(verify_token)
):
    """Stop email retrieval for a specific configuration"""
    try:
        db = firestore.client()
        user_id = user_data['uid']

        # Get the configuration
        config_ref = db.collection('users').document(user_id).collection('email_configs').document(config_id)
        config_doc = config_ref.get()

        if not config_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Configuration not found"
            )

        # Disable the configuration
        config_ref.update({
            'enabled': False,
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        # Cancel the job
        email_scheduler.cancel_job(user_id, config_id)

        return {"success": True, "message": "Email retrieval stopped"}

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error stopping email retrieval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error stopping email retrieval: {str(e)}"
        )

@router.post("/retrieval/run-now")
async def run_email_retrieval_now(
    request: RunNowRequest,
    user_data: dict = Depends(verify_token)
):
    """Run email retrieval immediately for a specific configuration"""
    try:
        # Log the user data for debugging
        logger.info(f"User data in run_email_retrieval_now: {user_data}")

        # Check if uid exists in user_data
        if 'uid' not in user_data:
            logger.error(f"Missing uid in user_data: {user_data}")
            # Try to use user_id if available
            if 'user_id' in user_data:
                logger.info(f"Using user_id instead of uid: {user_data['user_id']}")
                user_id = user_data['user_id']
            else:
                logger.error("No uid or user_id found in user_data")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials: missing user ID"
                )
        else:
            user_id = user_data['uid']

        # Apply rate limiting
        if not check_rate_limit(user_id):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        logger.info(f"Running email retrieval now for user: {user_id}, config: {request.config_id}")
        db = firestore.client()

        # Get the configuration
        config_ref = db.collection('users').document(user_id).collection('email_configs').document(request.config_id)
        config_doc = config_ref.get()

        if not config_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Configuration not found"
            )

        config = config_doc.to_dict()
        config['id'] = request.config_id

        # Check if immediate_processing is requested
        if request.immediate_processing:
            # Mark this config for immediate processing by the background processor
            config_ref.update({
                'immediate_processing': True,
                'last_run': firestore.SERVER_TIMESTAMP
            })
            logger.info(f"Marked config {request.config_id} for immediate processing by background processor")
            success = True
        else:
            # Run the job immediately using the scheduler
            success = email_scheduler.run_job_now(user_id, request.config_id)

            if success:
                # Update the last run timestamp
                config_ref.update({
                    'last_run': firestore.SERVER_TIMESTAMP
                })

        if success:
            return {
                "success": True,
                "message": "Email retrieval started",
                "account_id": config.get('account_id')
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start email retrieval"
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error running email retrieval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running email retrieval: {str(e)}"
        )

@router.post("/batch-analyze")
@router.post("/analyze-batch")  # Add an alias for the endpoint
async def batch_analyze_emails(
    request: BatchAnalyzeRequest,
    background_tasks: BackgroundTasks,
    user_data: dict = Depends(verify_token)
):
    """Batch analyze multiple emails based on a search query with clean JSON structure"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Limit batch size
        max_results = min(request.max_results or 20, MAX_BATCH_SIZE)

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        firebase_user_id = user_data['uid']

        # Get Firestore client
        db = firestore.client()
        user_ref = db.collection('users').document(firebase_user_id)

        # Initialize variables to store results
        analyzed_emails = []
        accounts_to_process = []

        # If account_id is specified, use only that account
        if request.account_id:
            # Get the specific account from email_accounts collection
            account_doc = user_ref.collection('email_accounts').document(request.account_id).get()
            if account_doc.exists:
                account_data = account_doc.to_dict()
                if 'credentials' in account_data:
                    accounts_to_process.append({
                        'id': request.account_id,
                        'credentials': account_data['credentials'],
                        'email': account_data.get('email', f'Account {request.account_id}')
                    })
                    logger.info(f"Selected account {request.account_id} ({account_data.get('email', 'Unknown')}) for processing")
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No valid credentials found for account {request.account_id}"
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Account {request.account_id} not found"
                )
        else:
            # Process all accounts
            accounts_ref = user_ref.collection('email_accounts')
            accounts = accounts_ref.get()

            for account_doc in accounts:
                account_data = account_doc.to_dict()
                if 'credentials' in account_data:
                    accounts_to_process.append({
                        'id': account_doc.id,
                        'credentials': account_data['credentials'],
                        'email': account_data.get('email', f'Account {account_doc.id}')
                    })
                    logger.info(f"Added account {account_doc.id} ({account_data.get('email', 'Unknown')}) to processing list")

        if not accounts_to_process:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Gmail accounts found for this user"
            )

        logger.info(f"Processing {len(accounts_to_process)} account(s) for email retrieval")

        # Prepare the search query based on date parameters
        modified_query = request.query or ""

        # If specific date range is provided, use it
        if request.start_date and request.end_date:
            # Format dates for Gmail query
            start_formatted = request.start_date.replace('-', '/')
            end_date_obj = datetime.strptime(request.end_date, '%Y-%m-%d')
            next_day = end_date_obj + timedelta(days=1)
            next_day_formatted = next_day.strftime('%Y/%m/%d')

            date_query = f"after:{start_formatted} before:{next_day_formatted}"
            modified_query = date_query if not modified_query else f"{modified_query} {date_query}"
            logger.info(f"Using date range query: {date_query}")

        # If only start date is provided, search from that date until now
        elif request.start_date:
            start_formatted = request.start_date.replace('-', '/')
            date_query = f"after:{start_formatted}"
            modified_query = date_query if not modified_query else f"{modified_query} {date_query}"
            logger.info(f"Using start date only query: {date_query}")

        # If only end date is provided, search until that date
        elif request.end_date:
            end_date_obj = datetime.strptime(request.end_date, '%Y-%m-%d')
            next_day = end_date_obj + timedelta(days=1)
            next_day_formatted = next_day.strftime('%Y/%m/%d')

            date_query = f"before:{next_day_formatted}"
            modified_query = date_query if not modified_query else f"{modified_query} {date_query}"
            logger.info(f"Using end date only query: {date_query}")

        # If single date is provided, use it
        elif request.date:
            date_obj = datetime.strptime(request.date, '%Y-%m-%d')
            after_date = date_obj.strftime('%Y/%m/%d')
            before_date = (date_obj + timedelta(days=1)).strftime('%Y/%m/%d')
            date_query = f"after:{after_date} before:{before_date}"
            modified_query = date_query if not modified_query else f"{modified_query} {date_query}"
            logger.info(f"Using specific date query: {date_query}")

        # If no date parameters provided and no query specified, use today's date
        elif not modified_query:
            today = datetime.now().strftime('%Y/%m/%d')
            modified_query = f"after:{today}"
            logger.info(f"No query or date parameters provided, using today's date: {modified_query}")

        # Process each account
        for account in accounts_to_process:
            logger.info(f"Processing account {account['id']} ({account['email']})")

            try:
                # Create Gmail service
                service = get_gmail_service(credentials_dict=account['credentials'])

                # Check if we should use the last retrieval time to optimize the search
                final_query = modified_query
                if request.use_last_retrieval_time and not (request.start_date or request.end_date or request.date):
                    # Get the last retrieval timestamp for this specific account
                    last_retrieval_ref = db.collection('users').document(firebase_user_id).collection('metadata').document(f'email_retrieval_{account["id"]}')
                    last_retrieval_doc = last_retrieval_ref.get()

                    if last_retrieval_doc.exists and 'last_timestamp' in last_retrieval_doc.to_dict():
                        last_timestamp = last_retrieval_doc.to_dict()['last_timestamp']

                        # Convert to datetime
                        if isinstance(last_timestamp, str):
                            try:
                                last_retrieval_time = datetime.fromisoformat(last_timestamp)
                                formatted_date = last_retrieval_time.strftime('%Y/%m/%d')

                                if 'after:' not in final_query:
                                    if final_query:
                                        final_query = f"{final_query} after:{formatted_date}"
                                    else:
                                        final_query = f"after:{formatted_date}"

                                logger.info(f"Using last retrieval time for account {account['id']}: {formatted_date}")
                            except ValueError:
                                logger.warning(f"Invalid last_timestamp format for account {account['id']}: {last_timestamp}")

                # Search for emails with the modified query
                logger.info(f"Searching emails with query: '{final_query}' for account {account['id']}")
                message_ids = search_messages(service, 'me', final_query, max_results)
                logger.info(f"Found {len(message_ids)} emails matching the query for account {account['id']}")

                # Get already analyzed email IDs to avoid reanalysis if requested
                analyzed_email_ids = set()
                if request.analyze_only_new:
                    analyses_ref = db.collection('users').document(firebase_user_id).collection('email_analyses')
                    analyses_query = analyses_ref.where('account_id', '==', account['id']).get()
                    analyzed_email_ids = {doc.to_dict().get('email_id') for doc in analyses_query if doc.to_dict().get('email_id')}
                    logger.info(f"Found {len(analyzed_email_ids)} already analyzed emails for account {account['id']}")

                # Get details for each email and analyze directly
                for msg_id in message_ids:
                    try:
                        # Skip if already analyzed and analyze_only_new is True
                        if request.analyze_only_new and msg_id in analyzed_email_ids:
                            logger.info(f"Email {msg_id} already analyzed, skipping (analyze_only_new=True)")
                            continue

                        # Get email details
                        email_details = get_message_details(service, 'me', msg_id)

                        # Add the account info to the email details
                        email_details['account_id'] = account['id']
                        email_details['account_email'] = account['email']

                        # Parse the email date to a consistent format
                        received_date = None
                        try:
                            if 'date' in email_details:
                                date_str = email_details['date']
                                date_str = re.sub(r'\([A-Z]{3}\)|\b[A-Z]{3}\b', '', date_str)

                                for fmt in ['%a, %d %b %Y %H:%M:%S %z', '%d %b %Y %H:%M:%S %z', '%a, %d %b %Y %H:%M:%S']:
                                    try:
                                        received_date = datetime.strptime(date_str.strip(), fmt)
                                        break
                                    except ValueError:
                                        continue

                                if not received_date:
                                    from dateutil import parser
                                    received_date = parser.parse(date_str)

                                email_details['parsed_date'] = received_date.isoformat()
                                email_details['date_timestamp'] = int(received_date.timestamp())
                        except Exception as date_err:
                            logger.warning(f"Error parsing email date: {str(date_err)}")
                            received_date = datetime.now()
                            email_details['parsed_date'] = received_date.isoformat()
                            email_details['date_timestamp'] = int(received_date.timestamp())

                        # Check if this email has already been analyzed
                        email_analyses_ref = db.collection('users').document(firebase_user_id).collection('email_analyses')

                        # For emails with same ID but from different accounts, use composite ID for lookup
                        composite_id = f"{account['id']}_{msg_id}"
                        analysis_doc = email_analyses_ref.document(composite_id).get()

                        # Check if we have a valid analysis with actual analysis data
                        if analysis_doc.exists and analysis_doc.to_dict().get('analysis'):
                            # Use existing analysis
                            analysis_data = analysis_doc.to_dict()

                            # Create a clean version of email_details without binary data
                            clean_email_details = {}
                            for key, value in email_details.items():
                                if key == 'attachments':
                                    clean_attachments = []
                                    for attachment in value:
                                        clean_attachment = {}
                                        for att_key, att_value in attachment.items():
                                            if att_key != 'data':
                                                clean_attachment[att_key] = att_value
                                        clean_attachments.append(clean_attachment)
                                    clean_email_details[key] = clean_attachments
                                elif key == 'body' and isinstance(value, dict):
                                    clean_body = {}
                                    for body_key, body_value in value.items():
                                        if isinstance(body_value, bytes):
                                            try:
                                                clean_body[body_key] = body_value.decode('utf-8', errors='replace')
                                            except Exception as e:
                                                logger.error(f"Error decoding body content: {str(e)}")
                                                clean_body[body_key] = "[Binary content could not be decoded]"
                                        else:
                                            clean_body[body_key] = body_value
                                    clean_email_details[key] = clean_body
                                elif not isinstance(value, bytes):
                                    clean_email_details[key] = value

                            analyzed_emails.append({
                                'email': clean_email_details,
                                'analysis': analysis_data.get('analysis', {}),
                                'is_cached': True,
                                'account_id': account['id'],
                                'account_email': account['email']
                            })
                            logger.info(f"Using existing analysis for email {msg_id} from account {account['id']}")
                            continue
                        elif analysis_doc.exists:
                            logger.info(f"Analysis exists but missing analysis data for email {msg_id} from account {account['id']}, will reanalyze")
                        else:
                            logger.info(f"No analysis found for email {msg_id} from account {account['id']}, will analyze")

                        # Store the raw email with account ID
                        email_storage.store_raw_email(email_details, firebase_user_id, account['id'])

                        # Process attachments directly from Gmail
                        attachment_contents = []
                        if email_details.get('attachments') and len(email_details['attachments']) > 0:
                            for attachment in email_details['attachments']:
                                try:
                                    # Check if we have a valid attachment ID
                                    if 'id' not in attachment or not attachment['id']:
                                        logger.info(f"Attachment without ID: {attachment.get('filename', 'unknown')}. Will try to download using Gmail API.")
                                        continue

                                    # Check if we already have the attachment data
                                    if 'data' in attachment and attachment['data']:
                                        logger.info(f"Using pre-loaded attachment data for {attachment.get('filename', 'unknown')}")
                                        attachment_contents.append({
                                            'data': attachment['data'],
                                            'filename': attachment.get('filename', 'unknown'),
                                            'content_type': attachment.get('mimeType', attachment.get('content_type', 'application/octet-stream')),
                                            'size': len(attachment['data'])
                                        })
                                        continue

                                    # Get attachment data directly from Gmail API
                                    logger.info(f"Downloading attachment {attachment.get('filename', 'unknown')} with ID {attachment['id']}")
                                    attachment_data = service.users().messages().attachments().get(
                                        userId='me',
                                        messageId=msg_id,
                                        id=attachment['id']
                                    ).execute()

                                    # Decode the attachment data
                                    file_data = base64.urlsafe_b64decode(attachment_data['data'])

                                    attachment_contents.append({
                                        'data': file_data,
                                        'filename': attachment['filename'],
                                        'content_type': attachment['mimeType'],
                                        'size': attachment['size']
                                    })

                                    logger.info(f"Successfully fetched attachment directly: {attachment['filename']}")
                                except Exception as att_err:
                                    logger.error(f"Error processing attachment: {str(att_err)}")

                        # Analyze the email with direct attachment processing using clean structure
                        try:
                            logger.info(f"Analyzing email {msg_id} with Gemini AI")
                            # Add retry logic with exponential backoff
                            max_retries = 3
                            retry_count = 0

                            while retry_count < max_retries:
                                try:
                                    analysis_result = analyze_with_gemini_direct(
                                        api_key,
                                        email_details,
                                        None,  # No attachment texts
                                        attachment_contents
                                    )
                                    logger.info(f"Successfully analyzed email {msg_id}")
                                    break
                                except Exception as retry_err:
                                    retry_count += 1
                                    if retry_count >= max_retries:
                                        raise
                                    logger.warning(f"Gemini API error on attempt {retry_count}/{max_retries}: {str(retry_err)}. Retrying...")
                                    # Exponential backoff
                                    await asyncio.sleep(2 ** retry_count)
                        except Exception as analysis_err:
                            logger.error(f"Error analyzing email with Gemini: {str(analysis_err)}")
                            # Provide a default analysis result to avoid breaking the flow
                            analysis_result = {
                                'error': True,
                                'error_message': str(analysis_err),
                                'analysis_results': {
                                    'summary': 'Error analyzing email',
                                    'sentiment': 'neutral'
                                },
                                'category': 'other'
                            }

                        # Log the analysis result category for debugging purposes
                        category = analysis_result.get('category', 'unknown')
                        logger.info(f"Email {msg_id} analyzed with category: {category}")

                        # Check if the email category is 'purchase_order', 'invoice', or 'order' and trigger the webhook
                        webhook_categories = ['purchase_order', 'invoice', 'order', 'order_confirmation']

                        # IMPORTANT: Force analysis category for emails with purchase order attachments
                        attachment_names = [att.get('filename', '').lower() for att in email_details.get('attachments', [])]

                        # Check if any attachment name contains keywords related to purchase orders or invoices
                        po_keywords = ['purchase', 'order', 'invoice', 'po_', 'po-', 'purchaseorder']
                        has_po_attachment = any(any(keyword in name for keyword in po_keywords) for name in attachment_names)

                        # Extra check for filenames that match the exact pattern of purchase order documents
                        po_filename_patterns = [
                            'purchaseorder', 'po_', 'po-', 'order_',
                            'order-', 'invoice_', 'invoice-', 'receipt_',
                            'receipt-', 'bill_', 'bill-'
                        ]

                        # More aggressive check for filename patterns
                        is_po_file = False
                        for name in attachment_names:
                            for pattern in po_filename_patterns:
                                if pattern in name:
                                    is_po_file = True
                                    logger.info(f"Found purchase order pattern '{pattern}' in filename '{name}'")
                                    break

                        # Always categorize attachments with "purchaseorder" in the name as purchase orders
                        if has_po_attachment or is_po_file:
                            if category == 'other':
                                logger.info(f"Email {msg_id} has purchase order/invoice attachment but was categorized as '{category}'. Overriding to 'purchase_order'.")
                                analysis_result['category'] = 'purchase_order'
                                category = 'purchase_order'

                        should_trigger = not analysis_result.get('error', False) and (category in webhook_categories or has_po_attachment)

                        # If analysis is successful, store attachments in Firebase
                        if not analysis_result.get('error', False):
                            from firebase_admin import storage
                            bucket = storage.bucket()

                            # Initialize attachments array if it doesn't exist
                            if 'attachments' not in analysis_result:
                                analysis_result['attachments'] = []

                            for i, attachment in enumerate(attachment_contents):
                                try:
                                    # Store in Firebase Storage
                                    storage_path = f"users/{firebase_user_id}/attachments/{msg_id}/{attachment['filename']}"
                                    blob = bucket.blob(storage_path)

                                    # Create temporary file for upload
                                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                                        temp_file.write(attachment['data'])
                                        temp_file_path = temp_file.name

                                    try:
                                        # Set content type
                                        content_type = attachment['content_type']

                                        # Upload with content type
                                        blob.upload_from_filename(temp_file_path, content_type=content_type)

                                        # Generate URL for the attachment - replace spaces with %20 and / with %2F
                                        encoded_path = storage_path.replace('/', '%2F').replace(' ', '%20')
                                        url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{encoded_path}?alt=media"

                                        # Update or add attachment entry in analysis result
                                        if i < len(analysis_result['attachments']):
                                            analysis_result['attachments'][i]['url'] = url
                                            analysis_result['attachments'][i]['size'] = len(attachment['data'])
                                        else:
                                            # Add new attachment entry
                                            attachment_entry = {
                                                'filename': attachment['filename'],
                                                'content_type': content_type,
                                                'size': len(attachment['data']),
                                                'url': url,
                                                'analysis': {
                                                    'document_type': 'Document',
                                                    'extracted_data_confidence': 0.98,
                                                    'content_summary': f"Attachment: {attachment['filename']}"
                                                }
                                            }
                                            analysis_result['attachments'].append(attachment_entry)

                                        # Add viewer URL for PDFs and docs
                                        content_type = attachment['content_type'].lower()
                                        if content_type in ['application/pdf', 'application/msword',
                                                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                                            if i < len(analysis_result['attachments']):
                                                analysis_result['attachments'][i]['viewer_url'] = f"https://docs.google.com/viewer?url={url}&embedded=true"

                                        logger.info(f"Stored attachment in Firebase after analysis: {attachment['filename']} with URL: {url}")
                                    finally:
                                        # Clean up temp file
                                        os.unlink(temp_file_path)
                                except Exception as e:
                                    logger.error(f"Error storing attachment in Firebase: {str(e)}")

                        # Store the analysis result in Firestore with clean structure
                        analysis_data = {
                            # Basic metadata
                            'created_at': firestore.SERVER_TIMESTAMP,
                            'email_id': msg_id,
                            'composite_id': composite_id,
                            'account_id': account['id'],
                            'account_email': account['email'],
                            'user_id': firebase_user_id,

                            # Email metadata from original email data
                            'subject': email_details.get('subject', 'No Subject'),
                            'from': email_details.get('from', 'Unknown'),
                            'to': email_details.get('to', ''),
                            'cc': email_details.get('cc', ''),
                            'bcc': email_details.get('bcc', ''),
                            'date': email_details.get('date', 'Unknown Date'),
                            'received_date': email_details.get('parsed_date', datetime.now().isoformat()),
                            'date_timestamp': email_details.get('date_timestamp', int(datetime.now().timestamp())),
                            'has_attachments': bool(email_details.get('attachments')),
                            'email_fingerprint': analysis_result.get('email_fingerprint', ''),

                            # Store the complete clean analysis result
                            'analysis': analysis_result,

                            # Extract key fields for easier querying (clean structure)
                            'category': analysis_result.get('category', 'other'),
                            'summary': analysis_result.get('analysis_results', {}).get('summary', ''),
                            'sentiment': analysis_result.get('analysis_results', {}).get('sentiment', 'neutral'),
                            'total_amount': analysis_result.get('financial_details', {}).get('total_amount', ''),
                            'document_number': analysis_result.get('document_info', {}).get('document_number', ''),
                            'po_number': analysis_result.get('document_info', {}).get('po_number', ''),
                            'invoice_number': analysis_result.get('document_info', {}).get('invoice_number', ''),
                            'vendor_name': analysis_result.get('parties', {}).get('vendor', {}).get('name', ''),
                            'customer_name': analysis_result.get('parties', {}).get('customer', {}).get('name', ''),

                            # Key dates and action items from clean structure
                            'key_dates': analysis_result.get('analysis_results', {}).get('action_items', []),
                            'action_items': analysis_result.get('analysis_results', {}).get('action_items', []),

                            # Add timestamps for tracking
                            'analyzed_at': firestore.SERVER_TIMESTAMP,
                            'last_updated': firestore.SERVER_TIMESTAMP,

                            # Store attachment info
                            'attachments': analysis_result.get('attachments', [])
                        }

                        # Store in Firestore using the composite ID
                        email_analyses_ref.document(composite_id).set(analysis_data)

                        # Update the email document to indicate it has been analyzed
                        email_ref = db.collection('users').document(firebase_user_id).collection('emails').document(composite_id)
                        email_ref.update({
                            'has_analysis': True,
                            'analysis_timestamp': firestore.SERVER_TIMESTAMP
                        })

                        # TRIGGER WEBHOOK AFTER STORAGE
                        if should_trigger:
                            logger.info(f"Email {msg_id} categorized as '{category}' which is in webhook categories {webhook_categories}. Triggering webhook AFTER storage.")
                            try:
                                # Import webhook sender
                                from .webhook_sender import send_to_webhook

                                # Create complete webhook data structure using stored analysis_data
                                webhook_data = {
                                    **analysis_data,  # Include all analysis data
                                    'attachments': analysis_result.get('attachments', [])  # Ensure attachments with URLs
                                }

                                print(f"[WEBHOOK SYNC] Sending webhook SYNCHRONOUSLY for email {msg_id} AFTER storage")
                                logger.info(f"Sending webhook synchronously for email {msg_id} AFTER storage")

                                # Call webhook synchronously (this will block until complete)
                                webhook_success = send_to_webhook(webhook_data)

                                if webhook_success:
                                    print(f"[WEBHOOK SYNC] Successfully sent webhook for email {msg_id}")
                                    logger.info(f"Successfully sent webhook for email {msg_id}")
                                else:
                                    print(f"[WEBHOOK SYNC] Failed to send webhook for email {msg_id}")
                                    logger.error(f"Failed to send webhook for email {msg_id}")

                            except Exception as webhook_err:
                                logger.error(f"Error sending webhook for email {msg_id}: {str(webhook_err)}")
                                logger.error(f"WEBHOOK ERROR: Traceback: {traceback.format_exc()}")
                                print(f"[WEBHOOK SYNC ERROR] Failed to send webhook: {str(webhook_err)}")

                        # Create a clean version of email_details without binary data
                        clean_email_details = {}
                        for key, value in email_details.items():
                            if key == 'attachments':
                                # Create a clean version of attachments without binary data
                                clean_attachments = []
                                for attachment in value:
                                    clean_attachment = {}
                                    for att_key, att_value in attachment.items():
                                        # Skip binary data fields
                                        if att_key != 'data':
                                            clean_attachment[att_key] = att_value
                                    clean_attachments.append(clean_attachment)
                                clean_email_details[key] = clean_attachments
                            elif key == 'body' and isinstance(value, dict):
                                # Ensure body content is properly encoded
                                clean_body = {}
                                for body_key, body_value in value.items():
                                    if isinstance(body_value, bytes):
                                        try:
                                            clean_body[body_key] = body_value.decode('utf-8', errors='replace')
                                        except Exception as e:
                                            logger.error(f"Error decoding body content: {str(e)}")
                                            clean_body[body_key] = "[Binary content could not be decoded]"
                                    else:
                                        clean_body[body_key] = body_value
                                clean_email_details[key] = clean_body
                            elif not isinstance(value, bytes):
                                clean_email_details[key] = value

                        # Add to results with clean data
                        analyzed_emails.append({
                            'email': clean_email_details,
                            'analysis': analysis_result,
                            'is_cached': False,
                            'account_id': account['id'],
                            'account_email': account['email']
                        })

                    except Exception as email_err:
                        logger.error(f"Error processing email {msg_id}: {str(email_err)}")
                        logger.error(f"Error processing email {msg_id}: {traceback.format_exc()}")
                        # Continue with next email instead of failing the whole batch

                # Store the current timestamp as the last retrieval time for this specific account
                current_time = datetime.now()
                last_retrieval_ref = db.collection('users').document(firebase_user_id).collection('metadata').document(f'email_retrieval_{account["id"]}')

                # Include date range information in the metadata
                metadata = {
                    'last_timestamp': current_time.isoformat(),
                    'last_query': final_query,
                    'emails_retrieved': len(message_ids),
                    'account_id': account['id'],
                    'account_email': account['email'],
                    'updated_at': firestore.SERVER_TIMESTAMP
                }

                # Add date range information if provided
                if request.start_date:
                    metadata['start_date'] = request.start_date
                if request.end_date:
                    metadata['end_date'] = request.end_date
                if request.date:
                    metadata['date'] = request.date

                # Update or create the document with merged data
                last_retrieval_ref.set(metadata, merge=True)

                logger.info(f"Updated last retrieval time for account {account['id']}")
            except Exception as account_err:
                logger.error(f"Error processing account {account['id']}: {str(account_err)}")
                # Continue with next account instead of failing the whole batch

        # Return the results with clean structure
        return {
            "success": True,
            "analyzed_emails": analyzed_emails,
            "total_emails": len(analyzed_emails),
            "total_analyzed": len([email for email in analyzed_emails if not email['is_cached']]),
            "accounts_processed": [account['id'] for account in accounts_to_process],
            "query": request.query,
            "date_range": {
                "start_date": request.start_date,
                "end_date": request.end_date,
                "single_date": request.date
            },
            "last_retrieval_time": datetime.now().isoformat()
        }

    except Exception as e:
        # Handle HTTPException specifically to extract the detail message
        if isinstance(e, HTTPException):
            error_message = e.detail or f"HTTPException: {e.status_code}"
        else:
            error_message = str(e) if str(e) else f"{type(e).__name__}: No error message available"
        
        logger.error(f"Batch analysis error: {error_message}")
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Prepare the response status and message
        if isinstance(e, HTTPException):
            response_status = e.status_code
            response_detail = f"Error batch analyzing emails: {e.detail}"
        else:
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            response_detail = f"Error batch analyzing emails: {error_message}"

        # Check for network connectivity issues first
        if ("failed to connect" in error_message or
            "socket is null" in error_message or
            "tcp handshaker shutdown" in error_message or
            "ipv6:" in error_message or
            "ipv4:" in error_message or
            "Timeout" in error_message):
            # This is a network connectivity issue
            logger.warning(f"Network connectivity issue detected: {error_message}. Setting status code to 503.")
            response_status = status.HTTP_503_SERVICE_UNAVAILABLE
            response_detail = "Network connectivity issue detected. Please try again later."

        # Only try to log to Firestore if it's not a network connectivity issue
        if response_status != status.HTTP_503_SERVICE_UNAVAILABLE:
            try:
                db = firestore.client()
                system_error_ref = db.collection('system_errors').document()
                system_error_ref.set({
                    'error': error_message,
                    'error_type': type(e).__name__,
                    'timestamp': firestore.SERVER_TIMESTAMP,
                    'component': 'batch_email_router.batch_analyze_emails',
                    'status_code': response_status,
                    'response_detail': response_detail
                })
            except Exception as log_err:
                logger.error(f"Failed to log error to Firestore: {str(log_err)}")

        # Raise the appropriate exception
        logger.warning(f"Raising HTTPException with status_code={response_status}, detail='{response_detail}'")
        raise HTTPException(
            status_code=response_status,
            detail=response_detail
        )

async def process_historical_analysis(user_id: str, task_id: str, batch_request: BatchAnalyzeRequest):
    """Process historical email analysis in the background with clean structure"""

    db = firestore.client()
    task_ref = db.collection('users').document(user_id).collection('batch_tasks').document(task_id)

    try:
        logger.info(f"Starting historical analysis task {task_id} for user {user_id}")

        # Update task status
        task_ref.update({
            'status': 'processing',
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        # Create a simple user data dictionary for authentication
        fake_user_data = {'uid': user_id}

        # Call the batch analyze function directly
        result = await batch_analyze_emails(batch_request, fake_user_data)

        # Update task with results
        task_ref.update({
            'status': 'completed',
            'completed_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP,
            'results': {
                'total_emails': result.get('total_emails', 0),
                'total_analyzed': result.get('total_analyzed', 0),
                'accounts_processed': result.get('accounts_processed', [])
            }
        })

        logger.info(f"Historical analysis task {task_id} completed successfully")

    except Exception as e:
        logger.error(f"Error in historical analysis task {task_id}: {str(e)}")
        # Update task with error
        task_ref.update({
            'status': 'failed',
            'error': str(e),
            'updated_at': firestore.SERVER_TIMESTAMP
        })



@router.get("/job-status/{config_id}")
async def get_job_status(
    config_id: str,
    user_data: dict = Depends(verify_token)
):
    """Get the status of a scheduled job"""
    try:
        job_id = f"{user_data['uid']}_{config_id}"

        # Get job details from the scheduler
        job_details = email_scheduler.get_job_details(job_id)

        if not job_details:
            # Try to get from Firestore
            db = firestore.client()
            job_ref = db.collection('email_scheduler_jobs').document(job_id)
            job_doc = job_ref.get()

            if not job_doc.exists:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Job not found"
                )

            job_details = job_doc.to_dict()

        # Get recent run history
        run_history = email_scheduler.get_job_run_history(job_id)

        return {
            "job_id": job_id,
            "status": job_details.get('status', 'unknown'),
            "last_run": job_details.get('last_run'),
            "next_run": job_details.get('next_run'),
            "run_count": job_details.get('run_count', 0),
            "success_count": job_details.get('success_count', 0),
            "error_count": job_details.get('error_count', 0),
            "account_id": job_details.get('account_id'),
            "run_history": run_history
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job status: {str(e)}"
        )

@router.post("/historical-analysis")
async def analyze_historical_emails(
    request: HistoricalEmailRequest,
    background_tasks: BackgroundTasks,
    user_data: dict = Depends(verify_token)
):
    """
    Analyze historical emails from a specific time period.
    This endpoint allows users to analyze emails from predefined time periods or custom date ranges.
    """
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        user_id = user_data['uid']
        logger.info(f"Historical email analysis requested by user {user_id} for period: {request.time_period}")

        # Calculate date range based on the requested time period
        start_date = None
        end_date = None
        today = datetime.now()

        if request.time_period == 'last_week':
            # Last 7 days
            start_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')
        elif request.time_period == 'last_month':
            # Last 30 days
            start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')
        elif request.time_period == 'last_3_months':
            # Last 90 days
            start_date = (today - timedelta(days=90)).strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')
        elif request.time_period == 'last_year':
            # Last 365 days
            start_date = (today - timedelta(days=365)).strftime('%Y-%m-%d')
            end_date = today.strftime('%Y-%m-%d')
        elif request.time_period == 'custom':
            # Use custom date range provided in the request
            if not request.start_date or not request.end_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Custom time period requires both start_date and end_date"
                )
            start_date = request.start_date
            end_date = request.end_date
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid time_period. Must be one of: 'last_week', 'last_month', 'last_3_months', 'last_year', 'custom'"
            )

        logger.info(f"Date range calculated: {start_date} to {end_date}")

        # Create a query for Gmail based on the date range
        query = f"after:{start_date.replace('-', '/')} before:{end_date.replace('-', '/')}"

        # Add category filter if specified
        if request.category_filter:
            if request.category_filter == 'orders':
                query += " (order OR purchase OR receipt OR confirmation OR invoice)"
            elif request.category_filter == 'finance':
                query += " (payment OR invoice OR statement OR bill OR finance OR bank)"
            elif request.category_filter == 'travel':
                query += " (flight OR hotel OR reservation OR booking OR itinerary OR travel)"
            elif request.category_filter == 'shipping':
                query += " (shipping OR delivery OR shipment OR tracking OR shipped)"
            else:
                query += f" {request.category_filter}"  # Add custom filter directly

        # Create a batch analyze request using the calculated parameters
        batch_request = BatchAnalyzeRequest(
            query=query,
            start_date=start_date,
            end_date=end_date,
            max_results=request.max_results,
            account_id=request.account_id,
            analyze_only_new=True  # Only analyze new emails by default
        )

        # Create a task ID for tracking the progress of this operation
        task_id = f"historical_{user_id}_{int(time.time())}"

        # Store the task information in Firestore for progress tracking
        db = firestore.client()
        task_ref = db.collection('users').document(user_id).collection('batch_tasks').document(task_id)
        task_ref.set({
            'type': 'historical_analysis',
            'time_period': request.time_period,
            'start_date': start_date,
            'end_date': end_date,
            'max_results': request.max_results,
            'account_id': request.account_id,
            'category_filter': request.category_filter,
            'status': 'in_progress',
            'created_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        # Instead of running the analysis directly, use a background task
        background_tasks.add_task(
            process_historical_analysis,
            user_id=user_id,
            task_id=task_id,
            batch_request=batch_request
        )

        return {
            "success": True,
            "message": f"Historical email analysis started for {request.time_period}",
            "date_range": {
                "start_date": start_date,
                "end_date": end_date
            },
            "task_id": task_id,
            "status": "in_progress"
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error analyzing historical emails: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing historical emails: {str(e)}"
        )

@router.post("/process-single")
async def process_single_email_enhanced(
    request: SingleEmailProcessRequest,
    background_tasks: BackgroundTasks,
    service_auth: dict = Depends(auth_router.verify_service_token)
):
    """Enhanced single email processing with robust error handling and monitoring"""
    start_time = time.time()
    request_id = f"{request.user_id}_{request.email_id}_{int(start_time)}"

    try:
        logger.info(f"[{request_id}] Processing single email {request.email_id} for user {request.user_id} from source: {request.source}")

        # Validate request parameters
        if not request.email_id or not request.user_id or not request.account_id:
            logger.error(f"[{request_id}] Missing required parameters")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required parameters: email_id, user_id, or account_id"
            )

        # Initialize Firestore client with retry logic
        try:
            db = firestore.client()
        except Exception as db_error:
            logger.error(f"[{request_id}] Failed to initialize Firestore client: {str(db_error)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database connection failed"
            )

        # Check if user exists with enhanced error handling
        try:
            user_ref = db.collection('users').document(request.user_id)
            user_doc = user_ref.get()

            if not user_doc.exists:
                logger.error(f"[{request_id}] User {request.user_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"User {request.user_id} not found"
                )
        except Exception as user_check_error:
            logger.error(f"[{request_id}] Error checking user existence: {str(user_check_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error validating user"
            )

        # Check if account exists and user has access with enhanced error handling
        try:
            account_ref = user_ref.collection('email_accounts').document(request.account_id)
            account_doc = account_ref.get()

            if not account_doc.exists:
                logger.error(f"[{request_id}] Account {request.account_id} not found for user {request.user_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Account {request.account_id} not found for user {request.user_id}"
                )

            # Validate account has necessary credentials
            account_data = account_doc.to_dict()
            if not account_data.get('credentials'):
                logger.error(f"[{request_id}] Account {request.account_id} missing credentials")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Account missing required credentials"
                )

        except HTTPException:
            raise
        except Exception as account_check_error:
            logger.error(f"[{request_id}] Error checking account: {str(account_check_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error validating account"
            )

        # Check if email has already been processed with enhanced duplicate detection
        try:
            composite_id = f"{request.account_id}_{request.email_id}"
            analysis_ref = user_ref.collection('email_analyses').document(composite_id)
            analysis_doc = analysis_ref.get()

            if analysis_doc.exists:
                analysis_data = analysis_doc.to_dict()
                processing_time = time.time() - start_time
                logger.info(f"[{request_id}] Email {request.email_id} already analyzed, skipping (took {processing_time:.3f}s)")

                return {
                    "success": True,
                    "message": "Email already processed",
                    "email_id": request.email_id,
                    "analysis_exists": True,
                    "category": analysis_data.get('category', 'unknown'),
                    "source": request.source,
                    "processing_time_ms": round(processing_time * 1000, 2),
                    "timestamp": datetime.now().isoformat(),
                    "request_id": request_id
                }
        except Exception as duplicate_check_error:
            logger.warning(f"[{request_id}] Error checking for duplicates: {str(duplicate_check_error)}")
            # Continue processing even if duplicate check fails

        # Process email in background task with enhanced monitoring
        try:
            background_tasks.add_task(
                process_single_email_background_enhanced,
                request.email_id,
                request.user_id,
                request.account_id,
                request.source,
                request_id,
                start_time
            )

            processing_time = time.time() - start_time
            logger.info(f"[{request_id}] Scheduled background processing for email {request.email_id} (took {processing_time:.3f}s)")

            return {
                "success": True,
                "message": "Email processing started",
                "email_id": request.email_id,
                "user_id": request.user_id,
                "account_id": request.account_id,
                "source": request.source,
                "status": "processing",
                "request_id": request_id,
                "processing_time_ms": round(processing_time * 1000, 2),
                "timestamp": datetime.now().isoformat(),
                "priority": request.priority
            }
        except Exception as task_error:
            logger.error(f"[{request_id}] Error scheduling background task: {str(task_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error scheduling email processing"
            )

    except HTTPException as e:
        processing_time = time.time() - start_time
        logger.error(f"[{request_id}] HTTP error processing single email: {e.detail} (took {processing_time:.3f}s)")
        raise e
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = str(e)
        logger.error(f"[{request_id}] Critical error processing single email: {error_msg} (took {processing_time:.3f}s)")
        logger.error(f"[{request_id}] Traceback: {traceback.format_exc()}")

        # Provide more specific error messages based on error type
        if "firestore" in error_msg.lower() or "database" in error_msg.lower():
            detail = "Database connection error. Please try again later."
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif "timeout" in error_msg.lower():
            detail = "Request timeout. Please try again later."
            status_code = status.HTTP_504_GATEWAY_TIMEOUT
        elif "permission" in error_msg.lower() or "access" in error_msg.lower():
            detail = "Access denied. Please check your permissions."
            status_code = status.HTTP_403_FORBIDDEN
        else:
            detail = f"Error processing single email: {error_msg}"
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

        raise HTTPException(status_code=status_code, detail=detail)

async def process_single_email_background_enhanced(
    email_id: str,
    user_id: str,
    account_id: str,
    source: str = "gmail_push_notification",
    request_id: str = None,
    start_time: float = None
):
    """Enhanced background task to process a single email with complete pipeline and robust error handling"""
    background_start_time = time.time()
    if not request_id:
        request_id = f"{user_id}_{email_id}_{int(background_start_time)}"

    try:
        logger.info(f"[{request_id}] Starting enhanced background processing for email {email_id}, user {user_id}, account {account_id}")

        # Initialize Firestore with retry logic
        max_db_retries = 3
        db = None
        for attempt in range(max_db_retries):
            try:
                db = firestore.client()
                break
            except Exception as db_error:
                if attempt < max_db_retries - 1:
                    logger.warning(f"[{request_id}] Database connection attempt {attempt + 1} failed: {str(db_error)}")
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"[{request_id}] Failed to connect to database after {max_db_retries} attempts")
                    return

        # Get account credentials with enhanced error handling
        try:
            account_ref = db.collection('users').document(user_id).collection('email_accounts').document(account_id)
            account_doc = account_ref.get()

            if not account_doc.exists:
                logger.error(f"[{request_id}] Account {account_id} not found during background processing")
                return

            account_data = account_doc.to_dict()
            if not account_data.get('credentials'):
                logger.error(f"[{request_id}] Account {account_id} missing credentials during background processing")
                return

        except Exception as account_error:
            logger.error(f"[{request_id}] Error retrieving account data: {str(account_error)}")
            return

        account_data = account_doc.to_dict()
        credentials_dict = account_data.get('credentials', {})

        if not credentials_dict:
            logger.error(f"No credentials found for account {account_id}")
            return

        # Get Gmail service
        service = get_gmail_service(credentials_dict=credentials_dict, user_id=user_id, account_id=account_id)

        # Check if email already exists in Firestore
        composite_id = f"{account_id}_{email_id}"
        email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
        email_doc = email_ref.get()

        email_data = None
        if email_doc.exists:
            # Use cached email data
            email_data = email_doc.to_dict()
            logger.info(f"Using cached email data for {email_id}")
        else:
            # Fetch email details from Gmail API
            logger.info(f"Fetching email {email_id} from Gmail API")
            email_data = get_message_details(service, msg_id=email_id)

            if not email_data or 'error' in email_data:
                logger.error(f"Failed to fetch email {email_id}: {email_data}")
                return

            # Add account ID to email data
            email_data['accountId'] = account_id
            email_data['source'] = source

            # Store raw email in Firestore
            email_storage.store_raw_email(email_data, user_id, account_id)
            logger.info(f"Stored raw email {email_id} in Firestore")

        # Get Gemini API key
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("Gemini API key not found in environment variables")
            return

        # Process attachments if any
        attachment_contents = []
        if email_data.get('has_attachments', False) and email_data.get('attachments'):
            logger.info(f"Processing {len(email_data['attachments'])} attachments for email {email_id}")

            for attachment in email_data['attachments']:
                try:
                    attachment_id = attachment.get('attachment_id')
                    if attachment_id and is_valid_attachment_id(attachment_id):
                        # Get attachment data from Gmail API
                        attachment_data = service.users().messages().attachments().get(
                            userId='me',
                            messageId=email_id,
                            id=attachment_id
                        ).execute()

                        decoded_data = base64.urlsafe_b64decode(attachment_data['data'])
                        logger.info(f"Successfully downloaded attachment {attachment.get('filename', 'unknown')} ({len(decoded_data)} bytes)")

                        attachment_contents.append({
                            'data': decoded_data,
                            'filename': attachment.get('filename', 'unknown'),
                            'content_type': attachment.get('mimeType', attachment.get('content_type', 'application/octet-stream')),
                            'size': len(decoded_data)
                        })
                except Exception as att_err:
                    logger.error(f"Error processing attachment {attachment.get('filename', 'unknown')}: {str(att_err)}")

        # Analyze the email with Gemini using clean structure
        try:
            logger.info(f"Analyzing email {email_id} with Gemini AI in background")
            analysis_result = analyze_with_gemini_direct(
                api_key,
                email_data,
                None,  # No attachment texts
                attachment_contents
            )

            if analysis_result.get('error', False):
                logger.error(f"Gemini analysis failed for email {email_id}: {analysis_result.get('error_message', 'Unknown error')}")
                return

            logger.info(f"Successfully analyzed email {email_id}")

        except Exception as analysis_err:
            logger.error(f"Error analyzing email with Gemini: {str(analysis_err)}")
            # Provide a default analysis result to avoid breaking the flow
            analysis_result = {
                'error': True,
                'error_message': str(analysis_err),
                'analysis_results': {
                    'summary': 'Error analyzing email',
                    'sentiment': 'neutral'
                },
                'category': 'other'
            }

        # Store analysis results
        composite_id = f"{account_id}_{email_id}"
        analysis_data = {
            **analysis_result,
            'email_id': email_id,
            'user_id': user_id,
            'account_id': account_id,
            'source': source,
            'processed_at': firestore.SERVER_TIMESTAMP
        }

        # Store in email_analyses collection
        analysis_ref = db.collection('users').document(user_id).collection('email_analyses').document(composite_id)
        analysis_ref.set(analysis_data)

        logger.info(f"Stored analysis results for email {email_id}")

        # Check if webhook should be triggered
        category = analysis_result.get('category', '').lower()
        webhook_categories = ['purchase_order', 'invoice', 'order_confirmation', 'order']

        if category in webhook_categories:
            logger.info(f"Email {email_id} categorized as '{category}' - triggering webhook")

            try:
                # Create webhook data structure
                webhook_data = {
                    **analysis_data,
                    'attachments': analysis_result.get('attachments', [])
                }

                # Send webhook
                send_to_webhook(webhook_data)
                logger.info(f"Successfully sent webhook for email {email_id}")

            except Exception as webhook_err:
                logger.error(f"Error sending webhook for email {email_id}: {str(webhook_err)}")

        logger.info(f"Completed background processing for email {email_id}")

    except Exception as e:
        logger.error(f"Error in background email processing: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

async def process_historical_analysis(user_id: str, task_id: str, batch_request: BatchAnalyzeRequest):
    """Process historical email analysis in the background"""

    db = firestore.client()
    task_ref = db.collection('users').document(user_id).collection('batch_tasks').document(task_id)

    try:
        logger.info(f"Starting historical analysis task {task_id} for user {user_id}")

        # Update task status
        task_ref.update({
            'status': 'processing',
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        # Create a simple user data dictionary for authentication
        fake_user_data = {'uid': user_id}

        # Call the batch analyze function directly
        result = await batch_analyze_emails(batch_request, fake_user_data)

        # Update task with results
        task_ref.update({
            'status': 'completed',
            'completed_at': firestore.SERVER_TIMESTAMP,
            'updated_at': firestore.SERVER_TIMESTAMP,
            'results': {
                'total_emails': result.get('total_emails', 0),
                'total_analyzed': result.get('total_analyzed', 0),
                'accounts_processed': result.get('accounts_processed', [])
            }
        })

        logger.info(f"Historical analysis task {task_id} completed successfully")

    except Exception as e:
        logger.error(f"Error in historical analysis task {task_id}: {str(e)}")
        # Update task with error
        task_ref.update({
            'status': 'failed',
            'error': str(e),
            'updated_at': firestore.SERVER_TIMESTAMP
        })

@router.get("/historical-analysis/{task_id}")
async def get_historical_analysis_status(
    task_id: str,
    user_data: dict = Depends(verify_token)
):
    """Get the status of a historical analysis task"""
    try:
        user_id = user_data['uid']
        db = firestore.client()
        task_ref = db.collection('users').document(user_id).collection('batch_tasks').document(task_id)
        task = task_ref.get()

        if not task.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Historical analysis task not found"
            )

        task_data = task.to_dict()

        # If the task is completed, also fetch a summary of the results
        results_summary = None
        if task_data.get('status') == 'completed':
            # Get the most recent analyses from this time period
            start_date = task_data.get('start_date')
            end_date = task_data.get('end_date')

            try:
                analyses_ref = db.collection('users').document(user_id).collection('email_analyses')

                # Create query to find all analyses from this date range
                # We need to convert string dates to timestamps for Firestore query
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # Include the end date

                # For simplicity, we'll just count analyses by their categories
                analyses = analyses_ref.where('date_timestamp', '>=', int(start_date_obj.timestamp())) \
                                     .where('date_timestamp', '<', int(end_date_obj.timestamp())).get()

                # Count by category
                category_counts = {}
                for doc in analyses:
                    analysis = doc.to_dict()
                    category = analysis.get('category', 'uncategorized')
                    if category in category_counts:
                        category_counts[category] += 1
                    else:
                        category_counts[category] = 1

                results_summary = {
                    'total_emails_found': len(analyses),
                    'categories': category_counts,
                }
            except Exception as summary_err:
                logger.error(f"Error getting results summary: {str(summary_err)}")

        response = {
            "task_id": task_id,
            "status": task_data.get('status', 'unknown'),
            "time_period": task_data.get('time_period'),
            "date_range": {
                "start_date": task_data.get('start_date'),
                "end_date": task_data.get('end_date')
            },
            "created_at": task_data.get('created_at'),
            "updated_at": task_data.get('updated_at')
        }

        if task_data.get('completed_at'):
            response['completed_at'] = task_data.get('completed_at')

        if task_data.get('results'):
            response['results'] = task_data.get('results')

        if task_data.get('error'):
            response['error'] = task_data.get('error')

        if results_summary:
            response['results_summary'] = results_summary

        return response

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting historical analysis status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting historical analysis status: {str(e)}"
        )