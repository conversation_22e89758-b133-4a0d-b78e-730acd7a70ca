# Cloud Run service configuration for Backend API
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: email-analyzer-backend
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "1"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300s"
        
        # Concurrency
        run.googleapis.com/execution-environment: gen2
        
        # VPC Connector (if needed for private resources)
        # run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/CONNECTOR_NAME
        
    spec:
      # Container concurrency
      containerConcurrency: 100
      
      # Timeout for requests
      timeoutSeconds: 300
      
      containers:
      - name: email-analyzer-backend
        image: gcr.io/PROJECT_ID/email-analyzer-backend:latest
        
        ports:
        - name: http1
          containerPort: 8080
          
        env:
        # Basic configuration
        - name: PORT
          value: "8080"
        - name: ENVIRONMENT
          value: "production"
        - name: WORKERS
          value: "1"
          
        # Performance settings
        - name: MAX_BATCH_SIZE
          value: "20"
        - name: RATE_LIMIT_WINDOW
          value: "60"
        - name: MAX_REQUESTS_PER_WINDOW
          value: "5"
        - name: REQUEST_TIMEOUT
          value: "30"
        - name: MAX_RETRIES
          value: "3"
          
        # Google Cloud settings
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: FIREBASE_STORAGE_BUCKET
          value: "PROJECT_ID.appspot.com"
          
        # Logging
        - name: LOG_LEVEL
          value: "INFO"
        - name: LOG_FORMAT
          value: "detailed"
          
        # Security settings
        - name: CORS_ORIGINS
          value: "*"
        - name: CORS_ALLOW_CREDENTIALS
          value: "true"
        - name: ALLOW_UNAUTHENTICATED
          value: "false"
          
        # Health check settings
        - name: HEALTH_CHECK_ENABLED
          value: "true"
        - name: HEALTH_CHECK_INTERVAL
          value: "30"
        - name: HEALTH_CHECK_TIMEOUT
          value: "5"
          
        # Metrics
        - name: METRICS_ENABLED
          value: "true"
        - name: PERFORMANCE_MONITORING
          value: "true"
          
        # Resource limits
        - name: MEMORY_LIMIT_MB
          value: "1024"
        - name: CPU_LIMIT
          value: "1.0"
          
        # Secrets from Secret Manager
        - name: SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: email-analyzer-service-token
              key: latest
              
        - name: FIREBASE_CREDENTIALS_PATH
          valueFrom:
            secretKeyRef:
              name: firebase-credentials
              key: latest
              
        - name: GMAIL_CREDENTIALS_JSON_PATH
          valueFrom:
            secretKeyRef:
              name: gmail-credentials
              key: latest
              
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: gemini-api-key
              key: latest
        
        # Resource requests and limits
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
            
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          
        # Startup probe for cold starts
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          
      # Service account for authentication
      serviceAccountName: email-analyzer-backend-sa
      
  traffic:
  - percent: 100
    latestRevision: true
