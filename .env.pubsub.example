# Enhanced Pub/Sub Handler Environment Configuration
# Copy this file to .env.pubsub and update with your actual values

# ============================================================================
# CORE SERVICE CONFIGURATION
# ============================================================================

# Google Cloud Project Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
PUBSUB_TOPIC=projects/your-project-id/topics/email-notifications
GOOGLE_APPLICATION_CREDENTIALS=backend/your-firebase-key.json

# Main Backend API Configuration
MAIN_FASTAPI_URL=http://localhost:8000
SERVICE_TOKEN=your-secure-service-token-here

# ============================================================================
# PERFORMANCE AND RELIABILITY SETTINGS
# ============================================================================

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY=1.0
REQUEST_TIMEOUT=30

# Rate Limiting
RATE_LIMIT_WINDOW=60
MAX_REQUESTS_PER_WINDOW=100
MAX_CONCURRENT_PROCESSING=10

# Email Processing Limits
MAX_EMAILS_PER_NOTIFICATION=10

# ============================================================================
# CIRCUIT BREAKER CONFIGURATION
# ============================================================================

# Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# ============================================================================
# HEALTH CHECK CONFIGURATION
# ============================================================================

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
FIRESTORE_HEALTH_CHECK_TIMEOUT=5

# ============================================================================
# LOGGING AND MONITORING
# ============================================================================

# Logging Configuration
LOG_LEVEL=INFO

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================

# Server Settings
PORT=8081
HOST=0.0.0.0
ENVIRONMENT=production
WORKERS=1

# ============================================================================
# SECURITY SETTINGS
# ============================================================================

# CORS Settings (only for development)
# CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# ============================================================================
# OPTIONAL ADVANCED SETTINGS
# ============================================================================

# Memory and Resource Limits
# MEMORY_LIMIT_MB=512
# CPU_LIMIT=1.0

# Database Connection Pool Settings
# DB_POOL_SIZE=10
# DB_MAX_OVERFLOW=20

# Async Settings
# ASYNC_POOL_SIZE=100
# ASYNC_TIMEOUT=60

# ============================================================================
# DEVELOPMENT SETTINGS (only for development environment)
# ============================================================================

# Development specific settings
# DEBUG=false
# RELOAD=false
# PROFILING_ENABLED=false

# ============================================================================
# MONITORING AND ALERTING
# ============================================================================

# Metrics Collection
# METRICS_ENABLED=true
# METRICS_ENDPOINT=/metrics

# Alerting Thresholds
# ERROR_RATE_THRESHOLD=5.0
# RESPONSE_TIME_THRESHOLD=1000
# MEMORY_USAGE_THRESHOLD=80.0

# ============================================================================
# BACKUP AND RECOVERY
# ============================================================================

# Backup Settings
# BACKUP_ENABLED=false
# BACKUP_INTERVAL=3600
# BACKUP_RETENTION_DAYS=7

# ============================================================================
# EXAMPLE VALUES FOR TESTING
# ============================================================================

# For local development, you can use these example values:
# GOOGLE_CLOUD_PROJECT=ai-email-bot-455814
# PUBSUB_TOPIC=projects/ai-email-bot-455814/topics/email-notifications
# MAIN_FASTAPI_URL=http://localhost:8000
# SERVICE_TOKEN=dev-service-token-12345
# ENVIRONMENT=development
# LOG_LEVEL=DEBUG
# MAX_REQUESTS_PER_WINDOW=1000
