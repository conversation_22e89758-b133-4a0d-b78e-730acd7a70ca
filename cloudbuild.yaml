# Cloud Build configuration for Email Analyzer System
# This builds and deploys both services to Cloud Run

steps:
  # Build Backend API Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-f'
      - 'backend/Dockerfile'
      - '-t'
      - 'gcr.io/$PROJECT_ID/email-analyzer-backend:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/email-analyzer-backend:latest'
      - 'backend/'

  # Build Pub/Sub Handler Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-pubsub'
    args:
      - 'build'
      - '-f'
      - 'Dockerfile.pubsub'
      - '-t'
      - 'gcr.io/$PROJECT_ID/email-analyzer-pubsub:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/email-analyzer-pubsub:latest'
      - '.'

  # Push Backend Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/email-analyzer-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # Push Pub/Sub Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-pubsub'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/email-analyzer-pubsub:$BUILD_ID'
    waitFor: ['build-pubsub']

  # Deploy Backend API to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-backend'
    args:
      - 'run'
      - 'deploy'
      - 'email-analyzer-backend'
      - '--image=gcr.io/$PROJECT_ID/email-analyzer-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=8080'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--concurrency=100'
      - '--max-instances=10'
      - '--min-instances=0'
      - '--timeout=300'
      - '--set-env-vars=ENVIRONMENT=production,PORT=8080'
      - '--set-secrets=SERVICE_TOKEN=email-analyzer-service-token:latest,FIREBASE_CREDENTIALS_PATH=firebase-credentials:latest,GMAIL_CREDENTIALS_JSON_PATH=gmail-credentials:latest,GEMINI_API_KEY=gemini-api-key:latest'
    waitFor: ['push-backend']

  # Deploy Pub/Sub Handler to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-pubsub'
    args:
      - 'run'
      - 'deploy'
      - 'email-analyzer-pubsub'
      - '--image=gcr.io/$PROJECT_ID/email-analyzer-pubsub:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--port=8080'
      - '--memory=512Mi'
      - '--cpu=1'
      - '--concurrency=50'
      - '--max-instances=5'
      - '--min-instances=0'
      - '--timeout=60'
      - '--set-env-vars=ENVIRONMENT=production,PORT=8080,GOOGLE_CLOUD_PROJECT=$PROJECT_ID'
      - '--set-secrets=SERVICE_TOKEN=email-analyzer-service-token:latest,GOOGLE_APPLICATION_CREDENTIALS=firebase-credentials:latest'
    waitFor: ['push-pubsub', 'deploy-backend']

  # Get Backend Service URL and Update Pub/Sub Handler
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'update-pubsub-config'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get the backend service URL
        BACKEND_URL=$(gcloud run services describe email-analyzer-backend --region=us-central1 --format='value(status.url)')
        echo "Backend URL: $BACKEND_URL"
        
        # Update the Pub/Sub handler with the backend URL
        gcloud run services update email-analyzer-pubsub \
          --region=us-central1 \
          --set-env-vars=MAIN_FASTAPI_URL=$BACKEND_URL,ENVIRONMENT=production,PORT=8080,GOOGLE_CLOUD_PROJECT=$PROJECT_ID
    waitFor: ['deploy-pubsub']

  # Configure Pub/Sub Topic Push Subscription
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'configure-pubsub'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get the Pub/Sub handler service URL
        PUBSUB_URL=$(gcloud run services describe email-analyzer-pubsub --region=us-central1 --format='value(status.url)')
        echo "Pub/Sub Handler URL: $PUBSUB_URL"
        
        # Create or update the push subscription
        if gcloud pubsub subscriptions describe email-notifications-push --quiet; then
          echo "Subscription exists, updating..."
          gcloud pubsub subscriptions modify-push-config email-notifications-push \
            --push-endpoint="$PUBSUB_URL/gmail-webhook"
        else
          echo "Creating new subscription..."
          gcloud pubsub subscriptions create email-notifications-push \
            --topic=email-notifications \
            --push-endpoint="$PUBSUB_URL/gmail-webhook" \
            --ack-deadline=60
        fi
    waitFor: ['update-pubsub-config']

  # Run Health Checks
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'health-check'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Running health checks..."
        
        # Get service URLs
        BACKEND_URL=$(gcloud run services describe email-analyzer-backend --region=us-central1 --format='value(status.url)')
        PUBSUB_URL=$(gcloud run services describe email-analyzer-pubsub --region=us-central1 --format='value(status.url)')
        
        echo "Backend URL: $BACKEND_URL"
        echo "Pub/Sub URL: $PUBSUB_URL"
        
        # Test backend health
        curl -f "$BACKEND_URL/health" || exit 1
        echo "✅ Backend health check passed"
        
        # Test pubsub health (with authentication)
        TOKEN=$(gcloud auth print-identity-token)
        curl -f -H "Authorization: Bearer $TOKEN" "$PUBSUB_URL/health" || exit 1
        echo "✅ Pub/Sub health check passed"
        
        echo "🎉 All services deployed successfully!"
        echo "Backend API: $BACKEND_URL"
        echo "Pub/Sub Handler: $PUBSUB_URL"
    waitFor: ['configure-pubsub']

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Timeout for the entire build
timeout: '1200s'

# Images to push to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/email-analyzer-backend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/email-analyzer-backend:latest'
  - 'gcr.io/$PROJECT_ID/email-analyzer-pubsub:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/email-analyzer-pubsub:latest'

# Substitutions
substitutions:
  _REGION: 'us-central1'
  _BACKEND_SERVICE: 'email-analyzer-backend'
  _PUBSUB_SERVICE: 'email-analyzer-pubsub'
