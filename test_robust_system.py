#!/usr/bin/env python3
"""
Enhanced Email Analyzer System Testing and Monitoring Script

This script tests the robust two-container email analyzer system
and provides comprehensive monitoring capabilities.
"""

import os
import sys
import json
import time
import requests
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class SystemTester:
    """Comprehensive system tester for the email analyzer"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.pubsub_url = "http://localhost:8081"
        self.test_results = []
        
    def print_header(self, title: str):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")

    def print_test(self, test_name: str):
        """Print test name"""
        print(f"\n🧪 Testing: {test_name}")

    def print_success(self, message: str):
        """Print success message"""
        print(f"✅ {message}")

    def print_warning(self, message: str):
        """Print warning message"""
        print(f"⚠️  {message}")

    def print_error(self, message: str):
        """Print error message"""
        print(f"❌ {message}")

    def record_test_result(self, test_name: str, success: bool, details: str = ""):
        """Record test result"""
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })

    def test_service_health(self, service_name: str, url: str) -> Tuple[bool, Dict]:
        """Test service health endpoint"""
        try:
            response = requests.get(f"{url}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                status = health_data.get('status', 'unknown')
                
                if status in ['healthy', 'degraded']:
                    self.print_success(f"{service_name} is {status}")
                    return True, health_data
                else:
                    self.print_error(f"{service_name} status: {status}")
                    return False, health_data
            else:
                self.print_error(f"{service_name} health check failed: HTTP {response.status_code}")
                return False, {}
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"{service_name} health check failed: {str(e)}")
            return False, {}

    def test_service_metrics(self, service_name: str, url: str) -> Tuple[bool, Dict]:
        """Test service metrics endpoint"""
        try:
            response = requests.get(f"{url}/metrics", timeout=10)
            
            if response.status_code == 200:
                metrics_data = response.json()
                self.print_success(f"{service_name} metrics available")
                return True, metrics_data
            else:
                self.print_error(f"{service_name} metrics failed: HTTP {response.status_code}")
                return False, {}
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"{service_name} metrics failed: {str(e)}")
            return False, {}

    def test_backend_endpoints(self) -> bool:
        """Test backend API endpoints"""
        self.print_test("Backend API Endpoints")
        
        endpoints = [
            ('Root', '/'),
            ('Health', '/health'),
            ('Metrics', '/metrics'),
        ]
        
        all_passed = True
        for name, endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    self.print_success(f"{name} endpoint: OK")
                else:
                    self.print_warning(f"{name} endpoint: HTTP {response.status_code}")
                    if response.status_code >= 500:
                        all_passed = False
            except Exception as e:
                self.print_error(f"{name} endpoint failed: {str(e)}")
                all_passed = False
        
        return all_passed

    def test_pubsub_endpoints(self) -> bool:
        """Test Pub/Sub handler endpoints"""
        self.print_test("Pub/Sub Handler Endpoints")
        
        endpoints = [
            ('Root', '/'),
            ('Health', '/health'),
            ('Metrics', '/metrics'),
        ]
        
        all_passed = True
        for name, endpoint in endpoints:
            try:
                response = requests.get(f"{self.pubsub_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    self.print_success(f"{name} endpoint: OK")
                else:
                    self.print_warning(f"{name} endpoint: HTTP {response.status_code}")
                    if response.status_code >= 500:
                        all_passed = False
            except Exception as e:
                self.print_error(f"{name} endpoint failed: {str(e)}")
                all_passed = False
        
        return all_passed

    def test_service_communication(self) -> bool:
        """Test communication between services"""
        self.print_test("Service Communication")
        
        try:
            # Get pubsub health to check if it can reach backend
            response = requests.get(f"{self.pubsub_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                main_fastapi_status = health_data.get('main_fastapi', {})
                
                if isinstance(main_fastapi_status, dict):
                    status = main_fastapi_status.get('status', 'unknown')
                    if status == 'connected':
                        self.print_success("Pub/Sub → Backend communication: OK")
                        return True
                    else:
                        self.print_error(f"Pub/Sub → Backend communication failed: {status}")
                        return False
                else:
                    self.print_warning("Pub/Sub → Backend communication status unclear")
                    return False
            else:
                self.print_error("Could not check service communication")
                return False
                
        except Exception as e:
            self.print_error(f"Service communication test failed: {str(e)}")
            return False

    def test_performance(self) -> bool:
        """Test system performance"""
        self.print_test("Performance Testing")
        
        try:
            # Test response times
            start_time = time.time()
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            backend_time = time.time() - start_time
            
            start_time = time.time()
            response = requests.get(f"{self.pubsub_url}/health", timeout=10)
            pubsub_time = time.time() - start_time
            
            self.print_success(f"Backend response time: {backend_time:.3f}s")
            self.print_success(f"Pub/Sub response time: {pubsub_time:.3f}s")
            
            # Check if response times are acceptable
            if backend_time < 2.0 and pubsub_time < 2.0:
                self.print_success("Response times are acceptable")
                return True
            else:
                self.print_warning("Response times are slow")
                return False
                
        except Exception as e:
            self.print_error(f"Performance test failed: {str(e)}")
            return False

    def monitor_system(self, duration: int = 60):
        """Monitor system for specified duration"""
        self.print_header(f"System Monitoring ({duration} seconds)")
        
        start_time = time.time()
        check_interval = 10  # Check every 10 seconds
        
        while time.time() - start_time < duration:
            print(f"\n📊 Monitoring check at {datetime.now().strftime('%H:%M:%S')}")
            
            # Check backend health
            backend_healthy, backend_health = self.test_service_health("Backend", self.backend_url)
            
            # Check pubsub health
            pubsub_healthy, pubsub_health = self.test_service_health("Pub/Sub", self.pubsub_url)
            
            # Display key metrics
            if backend_healthy and 'performance' in backend_health:
                perf = backend_health['performance']
                print(f"   Backend: {perf.get('notifications_processed', 0)} processed")
            
            if pubsub_healthy and 'performance' in pubsub_health:
                perf = pubsub_health['performance']
                print(f"   Pub/Sub: {perf.get('notifications_received', 0)} received")
            
            time.sleep(check_interval)

    def generate_report(self):
        """Generate test report"""
        self.print_header("Test Report")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")
        
        if failed_tests > 0:
            print("\nFailed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  ❌ {result['test']}: {result['details']}")
        
        # Save report to file
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': (passed_tests/total_tests*100) if total_tests > 0 else 0
            },
            'test_results': self.test_results
        }
        
        with open('test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        self.print_success("Test report saved to test_report.json")

    def run_comprehensive_tests(self):
        """Run all system tests"""
        self.print_header("Comprehensive System Testing")
        
        # Test suite
        tests = [
            ("Backend Health", lambda: self.test_service_health("Backend", self.backend_url)[0]),
            ("Pub/Sub Health", lambda: self.test_service_health("Pub/Sub", self.pubsub_url)[0]),
            ("Backend Endpoints", self.test_backend_endpoints),
            ("Pub/Sub Endpoints", self.test_pubsub_endpoints),
            ("Service Communication", self.test_service_communication),
            ("Performance", self.test_performance),
        ]
        
        for test_name, test_func in tests:
            self.print_test(test_name)
            try:
                success = test_func()
                self.record_test_result(test_name, success)
            except Exception as e:
                self.print_error(f"Test {test_name} crashed: {str(e)}")
                self.record_test_result(test_name, False, str(e))

def main():
    """Main testing function"""
    tester = SystemTester()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            tester.run_comprehensive_tests()
            tester.generate_report()
        elif command == "monitor":
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            tester.monitor_system(duration)
        elif command == "health":
            tester.test_service_health("Backend", tester.backend_url)
            tester.test_service_health("Pub/Sub", tester.pubsub_url)
        else:
            print("Usage: python test_robust_system.py [test|monitor|health] [duration]")
    else:
        print("Enhanced Email Analyzer System Tester")
        print("\nCommands:")
        print("  test     - Run comprehensive tests")
        print("  monitor  - Monitor system (default: 60 seconds)")
        print("  health   - Quick health check")
        print("\nExample: python test_robust_system.py test")

if __name__ == "__main__":
    main()
