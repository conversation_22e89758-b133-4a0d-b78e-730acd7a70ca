#!/bin/bash

# Complete Cloud Run Deployment Script for Email Analyzer System
# This script deploys both services to Google Cloud Run with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${1:-$(gcloud config get-value project)}
REGION=${2:-"us-central1"}

if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ Error: PROJECT_ID is required${NC}"
    echo "Usage: $0 <PROJECT_ID> [REGION]"
    exit 1
fi

echo -e "${BLUE}🚀 Deploying Email Analyzer System to Cloud Run${NC}"
echo -e "${BLUE}Project ID: $PROJECT_ID${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Validate prerequisites
echo -e "${BLUE}📋 Step 1: Validating prerequisites${NC}"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed"
    exit 1
fi

# Check if authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    print_error "Not authenticated with gcloud. Run: gcloud auth login"
    exit 1
fi

print_status "gcloud CLI is installed and authenticated"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    exit 1
fi

print_status "Docker is installed"

# Check required files
REQUIRED_FILES=(
    "pubsub_handler.py"
    "Dockerfile.pubsub"
    "backend/main.py"
    "backend/Dockerfile"
    "cloudbuild.yaml"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file not found: $file"
        exit 1
    fi
done

print_status "All required files found"

# Step 2: Setup IAM and secrets
echo -e "${BLUE}📋 Step 2: Setting up IAM and secrets${NC}"

if [ -f "setup-cloud-run-iam.sh" ]; then
    chmod +x setup-cloud-run-iam.sh
    ./setup-cloud-run-iam.sh $PROJECT_ID $REGION
    print_status "IAM setup completed"
else
    print_warning "setup-cloud-run-iam.sh not found, skipping IAM setup"
fi

# Step 3: Update secrets with real credentials
echo -e "${BLUE}📋 Step 3: Updating secrets with credentials${NC}"

# Check if credential files exist and update secrets
if [ -f "backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json" ]; then
    gcloud secrets versions add firebase-credentials \
        --data-file="backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json" \
        --project=$PROJECT_ID
    print_status "Updated Firebase credentials"
else
    print_warning "Firebase credentials file not found"
fi

if [ -f "backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json" ]; then
    gcloud secrets versions add gmail-credentials \
        --data-file="backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json" \
        --project=$PROJECT_ID
    print_status "Updated Gmail credentials"
else
    print_warning "Gmail credentials file not found"
fi

# Prompt for Gemini API key if not set
if [ -z "$GEMINI_API_KEY" ]; then
    echo -e "${YELLOW}🔑 Please enter your Gemini API key (or press Enter to skip):${NC}"
    read -s GEMINI_API_KEY
fi

if [ -n "$GEMINI_API_KEY" ] && [ "$GEMINI_API_KEY" != "" ]; then
    echo -n "$GEMINI_API_KEY" | gcloud secrets versions add gemini-api-key \
        --data-file=- \
        --project=$PROJECT_ID
    print_status "Updated Gemini API key"
else
    print_warning "Gemini API key not provided"
fi

# Step 4: Build and deploy using Cloud Build
echo -e "${BLUE}📋 Step 4: Building and deploying services${NC}"

# Substitute project ID in cloudbuild.yaml
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" cloudbuild.yaml
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" cloud-run-backend.yaml
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" cloud-run-pubsub.yaml

# Submit build
print_info "Starting Cloud Build deployment..."
gcloud builds submit --config=cloudbuild.yaml --project=$PROJECT_ID

print_status "Cloud Build deployment completed"

# Step 5: Get service URLs
echo -e "${BLUE}📋 Step 5: Getting service URLs${NC}"

BACKEND_URL=$(gcloud run services describe email-analyzer-backend \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(status.url)')

PUBSUB_URL=$(gcloud run services describe email-analyzer-pubsub \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(status.url)')

print_status "Backend URL: $BACKEND_URL"
print_status "Pub/Sub URL: $PUBSUB_URL"

# Step 6: Test deployments
echo -e "${BLUE}📋 Step 6: Testing deployments${NC}"

# Test backend health
print_info "Testing backend health..."
if curl -f -s "$BACKEND_URL/health" > /dev/null; then
    print_status "Backend health check passed"
else
    print_warning "Backend health check failed"
fi

# Test pubsub health (requires authentication)
print_info "Testing Pub/Sub handler health..."
TOKEN=$(gcloud auth print-identity-token)
if curl -f -s -H "Authorization: Bearer $TOKEN" "$PUBSUB_URL/health" > /dev/null; then
    print_status "Pub/Sub handler health check passed"
else
    print_warning "Pub/Sub handler health check failed"
fi

# Step 7: Configure Gmail push notifications
echo -e "${BLUE}📋 Step 7: Gmail push notification setup${NC}"

print_info "To complete the setup, configure Gmail push notifications:"
print_info "1. Go to Gmail API Console"
print_info "2. Set up push notifications with this URL: $PUBSUB_URL/gmail-webhook"
print_info "3. Use the Pub/Sub topic: projects/$PROJECT_ID/topics/email-notifications"

# Step 8: Display final information
echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo ""
echo -e "${BLUE}📊 Service Information:${NC}"
echo "=================================="
echo -e "${GREEN}Backend API:${NC} $BACKEND_URL"
echo -e "${GREEN}  Health:${NC} $BACKEND_URL/health"
echo -e "${GREEN}  Metrics:${NC} $BACKEND_URL/metrics"
echo -e "${GREEN}  Docs:${NC} $BACKEND_URL/docs"
echo ""
echo -e "${GREEN}Pub/Sub Handler:${NC} $PUBSUB_URL"
echo -e "${GREEN}  Health:${NC} $PUBSUB_URL/health"
echo -e "${GREEN}  Webhook:${NC} $PUBSUB_URL/gmail-webhook"
echo -e "${GREEN}  Metrics:${NC} $PUBSUB_URL/metrics"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "=================================="
echo "View logs:"
echo "  gcloud logs read --service=email-analyzer-backend --project=$PROJECT_ID"
echo "  gcloud logs read --service=email-analyzer-pubsub --project=$PROJECT_ID"
echo ""
echo "Update services:"
echo "  gcloud run services update email-analyzer-backend --region=$REGION --project=$PROJECT_ID"
echo "  gcloud run services update email-analyzer-pubsub --region=$REGION --project=$PROJECT_ID"
echo ""
echo "Scale services:"
echo "  gcloud run services update email-analyzer-backend --max-instances=20 --region=$REGION --project=$PROJECT_ID"
echo ""
echo -e "${BLUE}🔒 Security:${NC}"
echo "=================================="
echo "- Backend API: Public (allows unauthenticated)"
echo "- Pub/Sub Handler: Internal only (requires authentication)"
echo "- All secrets stored in Secret Manager"
echo ""
echo -e "${GREEN}✅ Your email analyzer system is now running on Cloud Run!${NC}"

# Restore original files
mv cloudbuild.yaml.bak cloudbuild.yaml 2>/dev/null || true
mv cloud-run-backend.yaml.bak cloud-run-backend.yaml 2>/dev/null || true
mv cloud-run-pubsub.yaml.bak cloud-run-pubsub.yaml 2>/dev/null || true
