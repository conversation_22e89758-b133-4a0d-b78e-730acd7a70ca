# Enhanced Main Backend Environment Configuration
# Copy this file to .env and update with your actual values

# ============================================================================
# CORE SERVICE CONFIGURATION
# ============================================================================

# Firebase Configuration
FIREBASE_CREDENTIALS_PATH=backend/your-firebase-key.json
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com

# Gmail API Configuration
GMAIL_CREDENTIALS_JSON_PATH=backend/your-gmail-credentials.json

# Gemini AI Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# ============================================================================
# SERVICE AUTHENTICATION
# ============================================================================

# Service Token for Internal API Communication
SERVICE_TOKEN=your-secure-service-token-here

# ============================================================================
# PERFORMANCE AND RELIABILITY SETTINGS
# ============================================================================

# Batch Processing Limits
MAX_BATCH_SIZE=20
RATE_LIMIT_WINDOW=60
MAX_REQUESTS_PER_WINDOW=5

# Request Timeouts
REQUEST_TIMEOUT=30
GEMINI_API_TIMEOUT=60

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY=1.0

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Firestore Settings
FIRESTORE_TIMEOUT=10
FIRESTORE_MAX_RETRIES=3

# ============================================================================
# EMAIL PROCESSING CONFIGURATION
# ============================================================================

# Email Analysis Settings
MAX_EMAILS_PER_BATCH=50
EMAIL_PROCESSING_TIMEOUT=300

# Attachment Processing
MAX_ATTACHMENT_SIZE_MB=25
SUPPORTED_ATTACHMENT_TYPES=pdf,docx,xlsx,txt

# ============================================================================
# LOGGING AND MONITORING
# ============================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=detailed

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================

# Server Settings
PORT=8080
HOST=0.0.0.0
ENVIRONMENT=production
WORKERS=1

# ============================================================================
# SECURITY SETTINGS
# ============================================================================

# CORS Settings
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true

# Authentication Settings
ALLOW_UNAUTHENTICATED=false
TOKEN_EXPIRY_HOURS=24

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================

# Caching Settings
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ============================================================================
# RATE LIMITING
# ============================================================================

# API Rate Limiting
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW=60

# Gemini API Rate Limiting
GEMINI_RATE_LIMIT_REQUESTS=60
GEMINI_RATE_LIMIT_WINDOW=60

# ============================================================================
# HEALTH CHECK CONFIGURATION
# ============================================================================

# Health Check Settings
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5

# ============================================================================
# DEVELOPMENT SETTINGS (only for development environment)
# ============================================================================

# Development specific settings
# DEBUG=false
# RELOAD=false
# DOCS_ENABLED=false
# PROFILING_ENABLED=false

# ============================================================================
# MONITORING AND ALERTING
# ============================================================================

# Metrics Collection
METRICS_ENABLED=true
METRICS_ENDPOINT=/metrics

# Performance Monitoring
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000

# ============================================================================
# BACKUP AND RECOVERY
# ============================================================================

# Data Backup Settings
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ============================================================================
# EXTERNAL INTEGRATIONS
# ============================================================================

# Third-party Service Settings
# WEBHOOK_TIMEOUT=30
# WEBHOOK_RETRIES=3

# ============================================================================
# RESOURCE LIMITS
# ============================================================================

# Memory and CPU Limits
MEMORY_LIMIT_MB=1024
CPU_LIMIT=2.0

# Connection Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# ============================================================================
# EXAMPLE VALUES FOR TESTING
# ============================================================================

# For local development, you can use these example values:
# FIREBASE_CREDENTIALS_PATH=backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json
# FIREBASE_STORAGE_BUCKET=ai-email-bot-455814.appspot.com
# GMAIL_CREDENTIALS_JSON_PATH=backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json
# SERVICE_TOKEN=dev-service-token-12345
# ENVIRONMENT=development
# LOG_LEVEL=DEBUG
# ALLOW_UNAUTHENTICATED=true
# MAX_REQUESTS_PER_WINDOW=1000
