# Enhanced Dockerfile for Email Analyzer Backend
FROM python:3.11-slim as builder

# Set build environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    libc6-dev \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip setuptools wheel \
    && pip install --no-cache-dir --user -r requirements.txt \
    && find /root/.local -name "*.pyc" -delete \
    && find /root/.local -name "__pycache__" -type d -exec rm -rf {} + || true

# Production stage
FROM python:3.11-slim

# Set enhanced environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    ENV=production \
    TZ=UTC \
    WORKERS=1 \
    MAX_BATCH_SIZE=20 \
    RATE_LIMIT_WINDOW=60 \
    MAX_REQUESTS_PER_WINDOW=5

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install runtime dependencies and security updates
RUN apt-get update && apt-get upgrade -y \
    && apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r -g 1001 appuser && useradd -r -g appuser -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder --chown=appuser:appuser /root/.local /home/<USER>/.local

# Copy project files with proper ownership
COPY --chown=appuser:appuser . .

# Make sure firebase credential files have correct permissions
RUN find . -name "*.json" -exec chmod 600 {} \; \
    && chown appuser:appuser *.json

# Create necessary directories with proper permissions
RUN mkdir -p /app/data/scheduler /app/logs /app/tmp \
    && chown -R appuser:appuser /app \
    && chmod 755 /app/data /app/logs /app/tmp

# Switch to non-root user
USER appuser

# Make sure scripts in .local are usable
ENV PATH=/home/<USER>/.local/bin:$PATH

# Environment variable for scheduler state
ENV SCHEDULER_DATA_PATH=/app/data/scheduler

# Expose the port that FastAPI will run on
EXPOSE 8080

# Enhanced healthcheck with better error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD python -c "\
import sys, requests, json; \
try: \
    response = requests.get('http://localhost:8080/health', timeout=8); \
    data = response.json(); \
    sys.exit(0 if data.get('status') in ['healthy', 'degraded'] else 1) \
except Exception as e: \
    print(f'Health check failed: {e}'); \
    sys.exit(1)"

# Enhanced startup command with better configuration
CMD ["uvicorn", "main:app", \
     "--host", "0.0.0.0", \
     "--port", "8080", \
     "--workers", "1", \
     "--access-log", \
     "--log-level", "info", \
     "--timeout-keep-alive", "30", \
     "--timeout-graceful-shutdown", "10"]
